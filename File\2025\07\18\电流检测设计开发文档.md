# T95电流检测模块设计开发文档

## 1. 概述

T95项目的电流检测模块主要用于实时监测电缆的接地电流和负载电流，为电力设备的安全运行提供重要的监测数据。该模块采用分层架构设计，包含数据采集、处理、存储和显示等完整功能。

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   硬件传感器     │───▶│   ZigBeeService  │───▶│ PeripheralService│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   视图层显示     │◀───│CurrentDetectionService│◀───│   数据处理层    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 电流检测完整时序图

```mermaid
sequenceDiagram
    participant User as 用户界面
    participant View as CurrentDetectionView
    participant Service as CurrentDetectionService
    participant Peripheral as PeripheralService
    participant Zig<PERSON><PERSON> as ZigBeeService
    participant Hardware as 硬件传感器
    participant Chart as CurrentDetectionChart
    participant DataSave as CurrentDetectionDataSave

    Note over User,DataSave: 1. 系统初始化阶段
    User->>View: 启动电流检测界面
    View->>Service: 创建CurrentDetectionService实例
    Service->>Service: 初始化定时器和线程
    View->>Chart: 创建图表组件
    Chart->>Chart: 初始化仪表盘和显示组件

    Note over User,DataSave: 2. 开始采样阶段
    User->>View: 点击开始采样按钮
    View->>Service: startSampleExt(userId)
    Service->>Service: 启动工作线程
    Service->>Service: 启动定时器(TIME_INTERVAL)
    Service-->>View: emit sigSignalChanged(SIGNAL_STATE_EXIST)
    View->>Chart: 更新信号状态显示

    Note over User,DataSave: 3. 定时采样循环
    loop 定时采样循环
        Service->>Service: timerEvent触发
        Service->>Service: singleSample()

        Note over Service,Hardware: 3.1 数据读取子流程
        Service->>Peripheral: readCurrentDetectionData(&stCableCurrentData)
        Peripheral->>ZigBee: readCurrentDetectionData(&stCableCurrentData)
        ZigBee->>Hardware: get_cable_current_data()
        Hardware-->>ZigBee: 返回原始电流数据
        ZigBee-->>Peripheral: 返回CableCurrentData{fGroundCurrent, fLoadCurrent}
        Peripheral-->>Service: 返回处理结果(0=成功)

        alt 数据读取成功
            Service->>Service: 重置失败计数器
            Service->>Service: 创建CurrentDetectionData
            Note right of Service: data.fGroundingCurrentValue = fGroundCurrent<br/>data.fLoadCurrentValue = fLoadCurrent
            Service->>Service: 获取订阅用户列表

            loop 向所有订阅用户发送数据
                Service-->>View: emit sigData(data, userId)
                View->>Chart: addSample(data)
                Chart->>Chart: updateGroundingCurrentText()
                Chart->>Chart: updateLoadCurrentText()
                Chart->>Chart: 计算接地/负载比值
                Chart->>Chart: 更新仪表盘显示

                alt 接地电流超过报警阈值
                    Chart->>Chart: 设置红色报警显示
                else 正常状态
                    Chart->>Chart: 设置绿色正常显示
                end

                alt 比值超过阈值(20%)
                    Chart->>Chart: 显示比值报警
                end
            end

            Service->>Service: updateSignalState(SIGNAL_STATE_EXIST)

        else 数据读取失败
            Service->>Service: 增加失败计数器
            alt 连续失败5次
                Service->>Service: updateSignalState(SIGNAL_STATE_NONE)
                Service-->>View: emit sigSignalChanged(SIGNAL_STATE_NONE)
                View->>Chart: 更新无信号状态
            end
        end
    end

    Note over User,DataSave: 4. 数据存储阶段
    User->>View: 触发数据保存
    View->>DataSave: saveData(CurrentDetectionDataInfo)
    DataSave->>DataSave: 创建XML/二进制文件
    DataSave->>DataSave: 保存接地电流(A/B/C/N相)
    DataSave->>DataSave: 保存负载电流
    DataSave->>DataSave: 保存比值分析结果
    DataSave-->>View: 返回保存文件路径

    Note over User,DataSave: 5. 停止采样阶段
    User->>View: 点击停止采样按钮
    View->>Service: stopSampleExt()
    Service->>Service: 停止定时器
    Service->>Service: 停止工作线程
    Service-->>View: emit sigSignalChanged(SIGNAL_STATE_NONE)
    View->>Chart: 清空显示数据

    Note over User,DataSave: 6. 诊断分析阶段
    User->>View: 请求电流诊断
    View->>Service: getCurrentInfo(stCurrentInfo)
    Service->>Peripheral: readCurrentDetectionData()
    Peripheral-->>Service: 返回电流数据
    Service->>Service: getCurrentDiagVal(fCurrentValue)
    Note right of Service: 0=正常(<100A)<br/>1=一般(100-200A)<br/>2=严重(>200A)
    Service-->>View: 返回诊断结果
    View->>Chart: 显示诊断结果
```

### 2.3 时序图关键阶段说明

#### 阶段1: 系统初始化
- 用户启动电流检测界面
- 创建各个服务组件实例
- 初始化图表和显示组件

#### 阶段2: 开始采样
- 用户触发采样开始
- 启动定时器和工作线程
- 更新界面状态显示

#### 阶段3: 定时采样循环（核心流程）
**数据读取子流程**:
1. 定时器触发采样事件
2. 通过PeripheralService调用ZigBeeService
3. ZigBeeService从硬件传感器获取原始数据
4. 返回包含接地电流和负载电流的CableCurrentData

**数据处理和显示**:
1. 将原始数据转换为业务数据结构
2. 分发给所有订阅用户
3. 更新图表显示（接地电流、负载电流）
4. 计算和显示比值分析
5. 根据阈值进行报警判断

**错误处理**:
- 连续5次读取失败则判定为无信号状态

#### 阶段4: 数据存储
- 保存完整的电流检测数据
- 支持XML和二进制两种格式
- 包含三相接地电流和负载电流数据

#### 阶段5: 停止采样
- 清理定时器和线程资源
- 更新界面状态

#### 阶段6: 诊断分析
- 提供电流诊断功能
- 基于阈值进行三级诊断判断

### 2.4 模块分层

1. **硬件接口层**: ZigBeeService - 负责与硬件传感器通信
2. **外设服务层**: PeripheralService - 统一的外设访问接口
3. **业务逻辑层**: CurrentDetectionService、CurrentService - 电流检测业务处理
4. **视图展示层**: CurrentDetectionView、CurrentDetectionChart - 用户界面和数据可视化

## 3. 核心数据结构

### 3.1 基础电流数据结构

```cpp
// 电缆电流测量终端数据类 (UHFHFCTAETEVApi.h)
typedef struct
{
    float fGroundCurrent;  //接地电流
    float fLoadCurrent;    //负载电流  
    UINT8 ucResv[16];      //保留字节
}CableCurrentData;
```

### 3.2 电流检测数据结构

```cpp
// 电流检测基础数据 (currentdetectiondefine.h)
struct CurrentDetectionData
{
    float fGroundingCurrentValue;  //当前接地电流数据
    float fLoadCurrentValue;       //当前负荷电流数据
};

// 电流检测图表数据 (currentdetectiondefine.h)  
struct CurrentDetectionChartData
{
    float fGroundingCurrentPhaseAValue;  //当前接地电流A相数据
    float fGroundingCurrentPhaseBValue;  //当前接地电流B相数据
    float fGroundingCurrentPhaseCValue;  //当前接地电流C相数据
    float fGroundingCurrentPhaseNValue;  //总接地电流数据
    float fLoadCurrentValue;             //当前负荷电流数据
    float fGroundingToLoadRatio;         //当前三相中最大接地电流与负荷电流比值
    float fGroundingMaxToMinRatio;       //当前单相最大接地电流与最小接地电流比值
};
```

### 3.3 电流信息结构

```cpp
// 电缆电流信息 (Module.h)
typedef struct _CabelCurrentInfo_
{
    float fLoadCurrentVal;   //负荷电流
    float fGroundCurrentVal; //接地电流
}CabelCurrentInfo;
```

## 4. 核心服务模块

### 4.1 PeripheralService (外设服务)

**文件位置**: `Z200\module\peripheral\peripheralservice.h/cpp`

**主要功能**:
- 提供统一的外设访问接口
- 封装底层硬件通信细节
- 管理电流检测数据的读取

**核心接口**:
```cpp
// 获取电流检测数据
int readCurrentDetectionData(CableCurrentData* pstCableCurrentData);
```

**实现细节**:
- 在ARM平台下调用ZigBeeService获取真实数据
- 在其他平台生成模拟数据用于测试
- 返回值：0表示成功，其他值表示失败

### 4.2 CurrentDetectionService (电流检测服务)

**文件位置**: `Z200\module\currentdetection\currentdetectionservice.h/cpp`

**主要功能**:
- 定时采集电流数据
- 管理采样状态和信号状态
- 向订阅者发送电流数据

**核心流程**:
1. **启动采样**: `startSampleExt()` - 启动定时器开始采样
2. **单次采样**: `singleSample()` - 读取一次电流数据
3. **数据处理**: 将CableCurrentData转换为CurrentDetectionData
4. **数据分发**: 通过信号发送给所有订阅用户

**关键代码片段**:
```cpp
void CurrentDetectionService::singleSample()
{
    CableCurrentData stCableCurrentData;
    memset(&stCableCurrentData, 0, sizeof(CableCurrentData));

    if (HC_SUCCESS == PeripheralService::instance()->readCurrentDetectionData(&stCableCurrentData))
    {
        CurrentDetection::CurrentDetectionData data;
        data.fGroundingCurrentValue = stCableCurrentData.fGroundCurrent;  //接地电流
        data.fLoadCurrentValue = stCableCurrentData.fLoadCurrent;         //负载电流

        // 发送数据给所有订阅用户
        QVector<MultiServiceNS::USERID> sampleUser = sampleUsers(MultiServiceNS::SPECTRUM_CURRENTDETECTION);
        for (int i = 0; i < sampleUser.size(); ++i)
        {
            emit sigData(data, sampleUser[i]);
        }
        
        updateSignalState(Module::SIGNAL_STATE_EXIST);
    }
    else
    {
        ++m_uiReadDataFailCnt;
        // 累积读取5次失败认为无信号
        if (m_uiReadDataFailCnt >= NO_SIGNAL_THREADSHOLD_CHECK)
        {
            updateSignalState(Module::SIGNAL_STATE_NONE);
        }
    }
}
```

### 4.3 CurrentService (电流服务)

**文件位置**: `Z200\module\current\currentservice.h/cpp`

**主要功能**:
- 提供同步和异步的电流读取接口
- 实现电流诊断功能
- 管理电流读取的超时机制

**电流诊断算法**:
```cpp
int CurrentService::getCurrentDiagVal(float fVal)
{
    int iRet = 1;  // 默认一般状态
    
    if(CURRENT_DIAG_HIGHRISK < fVal)      // > 200A
    {
        iRet = 2;  // 严重
    }
    else if(CURRENT_DIAG_WARNING > fVal)  // < 100A  
    {
        iRet = 0;  // 正常
    }
    
    return iRet;
}
```

**诊断等级**:
- 0: 正常 (< 100A)
- 1: 一般 (100A - 200A)
- 2: 严重 (> 200A)

## 5. 配置参数和阈值

### 5.1 报警阈值配置

```cpp
// 报警值枚举 (currentdetectiondefine.h)
enum AlarmValue
{
    // 接地报警值
    GROUNDING_ALARM_MIN = 1,
    GROUNDING_ALARM_MAX = 500,
    GROUNDING_ALARM_DEFAULT = 100,
    GROUNDING_ALARM_STEP = 1,

    // 负荷报警值  
    LOAD_ALARM_MIN = 1,
    LOAD_ALARM_MAX = 5000,
    LOAD_ALARM_DEFAULT = 300,
    LOAD_ALARM_STEP = 1
};
```

### 5.2 量程档位配置

```cpp
// 电流测试类型 (currentdetectiondefine.h)
enum CurrentRangeGear
{
    CURRENT_RANGE_GEAR_10A = 0,    // 10A档
    CURRENT_RANGE_GEAR_500A,       // 500A档
    CURRENT_RANGE_GEAR_5000A,      // 5000A档
    
    CURRENT_RANGE_GEAR_DEFAULT = CURRENT_RANGE_GEAR_10A,
};
```

### 5.3 电流相别类型

```cpp
// 电流相别类型 (currentdetectiondefine.h)
enum CurrentPhaseType
{
    PHASE_A = 0,  // A相
    PHASE_B,      // B相  
    PHASE_C,      // C相
    PHASE_N       // N相(中性线)
};
```

## 6. 数据处理和上传机制

### 6.1 数据采集流程

1. **定时触发**: CurrentDetectionService通过定时器定时触发采样
2. **硬件读取**: 调用PeripheralService读取硬件数据
3. **数据转换**: 将CableCurrentData转换为业务数据结构
4. **精度处理**: 使用`Module::dealFloatPrecision()`保留1位小数
5. **数据分发**: 通过Qt信号机制发送给订阅者

### 6.2 数据上传处理

**接地电流和负载电流的处理方式**:

1. **数据来源**: 
   - 接地电流: `CableCurrentData.fGroundCurrent`
   - 负载电流: `CableCurrentData.fLoadCurrent`

2. **数据转换**:
   ```cpp
   // CurrentDetectionService中的转换
   data.fGroundingCurrentValue = stCableCurrentData.fGroundCurrent;
   data.fLoadCurrentValue = stCableCurrentData.fLoadCurrent;
   
   // CurrentService中的精度处理
   m_stCurrentInfo.fLoadCurrentVal = Module::dealFloatPrecision(stCableCurrentData.fLoadCurrent, 1);
   m_stCurrentInfo.fGroundCurrentVal = Module::dealFloatPrecision(stCableCurrentData.fGroundCurrent, 1);
   ```

3. **数据分发机制**:
   - 通过Qt的信号槽机制实现数据分发
   - 支持多用户订阅同一数据源
   - 实时性保证：定时器间隔可配置

### 6.3 数据存储

**存储结构** (currentdetectiondatasave.h):
```cpp
typedef struct _CurrentDetectionDataInfo
{
    DataMapHead stHeadInfo;                    //图谱通用的头部信息
    DataFileNS::AmpUnit eCurrentUnit;          //电流单位
    float fGroundingAlarmValue;                //接地报警值
    float fLoadAlarmValue;                     //负荷报警值
    float fGroundingCurrentPhaseAValue;        //接地电流A相数据
    float fGroundingCurrentPhaseBValue;        //接地电流B相数据  
    float fGroundingCurrentPhaseCValue;        //接地电流C相数据
    float fGroundingCurrentPhaseNValue;        //总接地电流数据
    float fLoadCurrentValue;                   //负荷电流数据
    float fGroundingToLoadRatio;               //接地电流与负荷电流比值
    float fGroundingMaxToMinRatio;             //最大最小接地电流比值
}CurrentDetectionDataInfo;
```

**存储功能**:
- 支持XML格式和二进制格式存储
- 包含完整的元数据信息
- 支持数据回放功能

## 7. 用户界面设计

### 7.1 视图组件

1. **CurrentDetectionView**: 主视图容器
2. **CurrentDetectionChart**: 电流图表显示
3. **AmmeterWidget**: 仪表盘组件，实时显示电流值

### 7.2 显示功能

1. **实时数据显示**: 
   - 仪表盘形式显示当前电流值
   - 支持不同量程的自动切换显示

2. **历史数据图表**:
   - 时间序列图表显示电流变化趋势
   - 支持最大值记录和显示

3. **状态指示**:
   - 信号状态指示（有信号/无信号）
   - 运行状态指示
   - 采样模式显示

### 7.3 自动量程功能

```cpp
// 自动量程选择逻辑
CurrentDetection::CurrentRangeGear selectOptimalRange(float fCurrentValue);
bool shouldSwitchRange(float fCurrentValue, CurrentDetection::CurrentRangeGear eCurrent);
void switchToRange(CurrentDetection::CurrentRangeGear eNewRange);
```

**量程切换策略**:
- 根据当前电流值自动选择最适合的量程档位
- 避免频繁切换，设置切换阈值
- 切换时保留历史最大值信息

## 8. 关键技术特点

### 8.1 多线程设计
- CurrentDetectionService运行在独立线程中
- 使用QMutex保证线程安全
- 通过Qt信号槽实现线程间通信

### 8.2 错误处理机制
- 连续读取失败计数机制
- 超时保护机制
- 信号状态自动更新

### 8.3 配置管理
- 支持采样模式配置
- 支持量程档位配置  
- 配置参数持久化存储

### 8.4 数据精度控制
- 统一的浮点数精度处理
- 避免浮点数累积误差
- 格式化显示去除无效零

## 9. 开发要点

### 9.1 关键接口
1. `PeripheralService::readCurrentDetectionData()` - 核心数据读取接口
2. `CurrentDetectionService::sigData()` - 数据分发信号
3. `CurrentService::getCurrentInfo()` - 同步数据获取接口

### 9.2 注意事项
1. 所有电流数据以安培(A)为单位
2. 接地电流和负载电流需要分别处理和显示
3. 需要考虑硬件平台差异（ARM vs 其他平台）
4. 数据采集频率需要平衡实时性和系统性能

### 9.3 扩展性设计
1. 支持多种电流传感器类型
2. 可配置的报警阈值
3. 灵活的数据存储格式
4. 模块化的视图组件设计

## 10. 接地电流与负载电流的使用场景详解

### 10.1 接地电流的使用场景

**定义**: 接地电流是指电缆绝缘层泄漏到地的电流，是电缆绝缘状态的重要指标。

**使用场景**:

1. **绝缘状态监测**:
   - 监测电缆绝缘层的老化程度
   - 检测绝缘层是否存在缺陷或损伤
   - 评估电缆的绝缘性能

2. **故障预警**:
   ```cpp
   // 接地电流报警逻辑 (bjcurrentdetectionchart.cpp)
   void BJCurrentDetectionChart::updateGroundingCurrentText(float fGroundingValue, const CurrentDetection::CurrentPhaseType eCurrentPhaseType)
   {
       QColor color;
       if (fGroundingValue >= m_uGroundingAlarm)  // 默认100A
       {
           color = Qt::red;    // 红色报警
       }
       else
       {
           color = Qt::darkGreen;  // 正常绿色
       }
   }
   ```

3. **三相不平衡检测**:
   - 分别监测A、B、C三相的接地电流
   - 计算各相接地电流的最大值与最小值比值
   - 当比值超过阈值(3.0)时报警

4. **相别选择监测**:
   - 支持按相别(PHASE_A/B/C/N)分别显示接地电流
   - 用户可选择查看特定相的接地电流数据
   - N相代表中性线的总接地电流

### 10.2 负载电流的使用场景

**定义**: 负载电流是指电缆正常工作时流过的电流，反映电缆的负荷状态。

**使用场景**:

1. **负荷监测**:
   - 监测电缆的实际负荷情况
   - 评估电缆的利用率
   - 防止过载运行

2. **容量规划**:
   - 为电网扩容提供数据支撑
   - 评估电缆的剩余容量
   - 优化负荷分配

3. **负载报警**:
   ```cpp
   // 负载电流报警阈值配置
   enum AlarmValue
   {
       LOAD_ALARM_MIN = 1,
       LOAD_ALARM_MAX = 5000,      // 最大5000A
       LOAD_ALARM_DEFAULT = 300,   // 默认300A报警
       LOAD_ALARM_STEP = 1
   };
   ```

### 10.3 两种电流的关联分析

**接地电流与负载电流比值分析**:

```cpp
// 比值计算和报警逻辑 (bjcurrentdetectionchart.cpp)
float fGroundingToLoadRatio = 0.0f;
if(!Module::fValEqual(m_data.fLoadCurrentValue, 0.0f))
{
    fGroundingToLoadRatio = getMaxGroundingCurrent() / m_data.fLoadCurrentValue;
}

// 比值报警阈值: 0.2 (20%)
#define GROUNDING_TO_LOAD_RATIO_THRESHOLD 0.2

void BJCurrentDetectionChart::updateGroundingToLoadRatioText(float fGroundingToLoadRatio)
{
    QColor color;
    if(static_cast<double>(fGroundingToLoadRatio) > GROUNDING_TO_LOAD_RATIO_THRESHOLD)
    {
        color = Qt::red;    // 比值过高，红色报警
    }
    else
    {
        color = Qt::darkGreen;  // 正常绿色
    }
}
```

**比值意义**:
- 当接地电流与负载电流比值超过20%时，表明绝缘状态异常
- 正常情况下，接地电流应远小于负载电流
- 比值过高可能表示绝缘老化或存在故障

### 10.4 不同应用模式下的使用差异

**1. 实时监测模式**:
- 接地电流: 连续监测各相接地电流，实时报警
- 负载电流: 监测当前负荷状态，防止过载

**2. 诊断分析模式**:
```cpp
// 电流诊断算法 (currentservice.cpp)
int CurrentService::getCurrentDiagVal(float fVal)
{
    // 注意：此诊断算法主要针对负载电流
    if(CURRENT_DIAG_HIGHRISK < fVal)      // > 200A
    {
        return 2;  // 严重 - 负荷过重
    }
    else if(CURRENT_DIAG_WARNING > fVal)  // < 100A
    {
        return 0;  // 正常 - 负荷正常
    }
    return 1;  // 一般 - 负荷较重
}
```

**3. 任务模式**:
- 接地电流: 按相别进行专项检测
- 负载电流: 记录检测时的负荷状态
- 用于生成检测报告和历史数据分析

### 10.5 数据存储和回放中的区别

**存储结构差异**:
```cpp
typedef struct _CurrentDetectionDataInfo
{
    // 接地电流 - 按相别分别存储
    float fGroundingCurrentPhaseAValue;  //A相接地电流
    float fGroundingCurrentPhaseBValue;  //B相接地电流
    float fGroundingCurrentPhaseCValue;  //C相接地电流
    float fGroundingCurrentPhaseNValue;  //总接地电流

    // 负载电流 - 单一值存储
    float fLoadCurrentValue;             //负荷电流

    // 关联分析数据
    float fGroundingToLoadRatio;         //接地/负荷比值
    float fGroundingMaxToMinRatio;       //接地电流最大最小比值
}CurrentDetectionDataInfo;
```

**回放显示差异**:
```cpp
// 回放时的数据设置 (bjcurrentdetectionchart.cpp)
void BJCurrentDetectionChart::setPlayBackData(const CurrentDetection::CurrentDetectionChartData& data)
{
    // 接地电流按相别分别显示
    updateGroundingCurrentText(data.fGroundingCurrentPhaseAValue, CurrentDetection::PHASE_A);
    updateGroundingCurrentText(data.fGroundingCurrentPhaseBValue, CurrentDetection::PHASE_B);
    updateGroundingCurrentText(data.fGroundingCurrentPhaseCValue, CurrentDetection::PHASE_C);
    updateGroundingCurrentText(data.fGroundingCurrentPhaseNValue, CurrentDetection::PHASE_N);

    // 负载电流统一显示
    updateLoadCurrentText(data.fLoadCurrentValue);
}
```

### 10.6 报警策略的差异

**接地电流报警**:
- 阈值范围: 1-500A，默认100A
- 报警原因: 绝缘性能下降，存在安全隐患
- 处理建议: 检查绝缘状态，必要时更换电缆

**负载电流报警**:
- 阈值范围: 1-5000A，默认300A
- 报警原因: 负荷过重，可能导致过热
- 处理建议: 调整负荷分配，避免过载运行

**比值报警**:
- 接地/负荷比值 > 20%: 绝缘状态异常
- 接地电流最大/最小比值 > 3.0: 三相不平衡

### 10.7 量程选择的考虑

**接地电流量程**:
- 通常较小，多在10A-500A范围
- 默认使用10A档位进行精确测量
- 异常情况下可能达到500A档位

**负载电流量程**:
- 变化范围大，可达5000A
- 需要根据实际负荷自动切换量程
- 支持10A/500A/5000A三档自动切换

## 11. 总结

T95电流检测模块通过分别监测接地电流和负载电流，实现了对电缆绝缘状态和负荷状态的全面监控：

- **接地电流**主要用于绝缘状态监测、故障预警和三相平衡分析
- **负载电流**主要用于负荷监测、容量规划和过载保护
- **两者关联**通过比值分析提供更准确的设备状态评估

该模块采用完整的分层架构设计，实现了从硬件数据采集到用户界面显示的完整数据流。通过合理的数据结构设计和处理机制，确保了接地电流和负载电流数据的准确采集、处理和展示。该模块具有良好的扩展性和维护性，为电力设备的安全监测提供了可靠的技术支撑。
