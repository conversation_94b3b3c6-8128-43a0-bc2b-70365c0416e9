# 负载/接地切换按钮时序图详细说明

## 1. 时序图概述

本文档详细描述负载/接地切换按钮在用户操作时的完整数据流和状态变化过程，包含两个主要视图的时序差异和关键节点的处理逻辑。

## 2. 核心时序图

### 2.1 完整切换流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant View as CurrentDetectionView
    participant But<PERSON><PERSON><PERSON> as PushButtonBar
    participant Chart as CurrentDetectionChart
    participant Config as ConfigManager
    participant Service as CurrentDetectionService
    participant Hardware as 硬件数据

    Note over User,Hardware: 阶段1: 系统初始化
    User->>UI: 启动电流检测模块
    UI->>View: 创建CurrentDetectionView实例
    View->>Config: loadCurrentDisplayTypeConfig()
    Config->>Config: 读取配置文件
    Config-->>View: 返回显示类型配置(默认:负载电流)
    View->>View: m_eCurrentDisplayType = CURRENT_DISPLAY_LOAD
    View->>ButtonBar: 创建按钮栏
    View->>ButtonBar: 设置切换按钮初始值
    View->>Chart: 创建图表组件
    View->>Chart: setCurrentDisplayType(CURRENT_DISPLAY_LOAD)
    Chart->>Chart: m_eCurrentDisplayType = CURRENT_DISPLAY_LOAD
    Chart->>Chart: updateCurrentTypeLabel("负载电流")

    Note over User,Hardware: 阶段2: 数据采集和显示
    loop 持续数据采集
        Hardware->>Service: 提供原始电流数据
        Service->>Service: 解析CableCurrentData
        Service->>View: onDataRead(CurrentDetectionData{fGroundCurrent, fLoadCurrent})
        View->>Chart: addSample(data)
        
        alt 当前显示负载电流
            Chart->>Chart: fDisplayValue = data.fLoadCurrentValue
            Chart->>Chart: updateCurrentChart(负载电流值)
            Chart->>Chart: 更新仪表盘显示(负载电流)
        else 当前显示接地电流
            Chart->>Chart: fDisplayValue = data.fGroundingCurrentValue
            Chart->>Chart: updateCurrentChart(接地电流值)
            Chart->>Chart: 更新仪表盘显示(接地电流)
            
            alt 接地电流超过阈值(100A)
                Chart->>Chart: 设置红色报警显示
            else 接地电流正常
                Chart->>Chart: 设置绿色正常显示
            end
        end
        
        Chart->>Chart: 更新最大值记录
        Chart->>Chart: 更新历史曲线
    end

    Note over User,Hardware: 阶段3: 用户切换操作
    User->>ButtonBar: 点击"电流类型"切换按钮
    ButtonBar->>ButtonBar: 切换按钮状态(负载电流→接地电流)
    ButtonBar->>View: onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, CURRENT_DISPLAY_GROUNDING)
    
    View->>View: 验证参数有效性
    alt 参数有效且值发生变化
        View->>View: m_eCurrentDisplayType = CURRENT_DISPLAY_GROUNDING
        View->>Chart: setCurrentDisplayType(CURRENT_DISPLAY_GROUNDING)
        Chart->>Chart: m_eCurrentDisplayType = CURRENT_DISPLAY_GROUNDING
        Chart->>Chart: clearHistoryDataOnly() // 清空历史数据，保留最大值
        Chart->>Chart: updateCurrentTypeLabel("接地电流")
        View->>Config: saveCurrentDisplayTypeConfig()
        Config->>Config: 持久化保存新配置
        View->>View: logDebug("显示类型已切换为接地电流")
    else 参数无效或值未变化
        View->>View: logWarning("忽略无效或重复的切换操作")
    end

    Note over User,Hardware: 阶段4: 切换后数据处理
    loop 切换后持续采集
        Hardware->>Service: 继续提供电流数据
        Service->>View: onDataRead(CurrentDetectionData)
        View->>Chart: addSample(data)
        
        Chart->>Chart: fDisplayValue = data.fGroundingCurrentValue // 现在显示接地电流
        Chart->>Chart: updateCurrentChart(接地电流值)
        Chart->>Chart: 更新仪表盘(接地电流模式)
        
        alt 接地电流报警检查
            Chart->>Chart: 检查接地电流阈值
            alt 超过100A阈值
                Chart->>Chart: 显示红色报警状态
                Chart->>Chart: 更新报警指示器
            else 正常范围
                Chart->>Chart: 显示绿色正常状态
            end
        end
        
        Chart->>Chart: 更新接地电流最大值
        Chart->>Chart: 绘制接地电流历史曲线
    end

    Note over User,Hardware: 阶段5: 再次切换回负载电流
    User->>ButtonBar: 再次点击切换按钮
    ButtonBar->>View: onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, CURRENT_DISPLAY_LOAD)
    View->>Chart: setCurrentDisplayType(CURRENT_DISPLAY_LOAD)
    Chart->>Chart: clearHistoryDataOnly()
    Chart->>Chart: updateCurrentTypeLabel("负载电流")
    View->>Config: saveCurrentDisplayTypeConfig()
    
    Note over User,Hardware: 后续数据显示回到负载电流模式
    Service->>View: onDataRead(CurrentDetectionData)
    View->>Chart: addSample(data)
    Chart->>Chart: fDisplayValue = data.fLoadCurrentValue // 重新显示负载电流
    Chart->>Chart: updateCurrentChart(负载电流值)
```

### 2.2 BJCurrentDetectionView 特殊时序

```mermaid
sequenceDiagram
    participant User as 用户
    participant BJView as BJCurrentDetectionView
    participant PhaseBtn as 相别选择按钮
    participant TypeBtn as 类型切换按钮
    participant BJChart as BJCurrentDetectionChart
    participant Service as CurrentDetectionService

    Note over User,Service: BJCurrentDetectionView 特有的相别+类型双重切换

    User->>PhaseBtn: 选择电流相别(例如:A相)
    PhaseBtn->>BJView: onButtonValueChanged(BUTTON_CURRENT_PHASE_SELECTION, PHASE_A)
    BJView->>BJChart: onPhaseTypeChanged(PHASE_A)
    BJChart->>BJChart: m_eCurrentPhaseType = PHASE_A

    User->>TypeBtn: 切换到接地电流显示
    TypeBtn->>BJView: onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, CURRENT_DISPLAY_GROUNDING)
    BJView->>BJChart: setCurrentDisplayType(CURRENT_DISPLAY_GROUNDING)
    BJChart->>BJChart: m_eCurrentDisplayType = CURRENT_DISPLAY_GROUNDING

    Service->>BJView: onDataRead(CurrentDetectionData)
    BJView->>BJChart: addSample(data)
    
    alt 显示接地电流模式
        BJChart->>BJChart: 根据相别显示对应接地电流
        alt A相选中
            BJChart->>BJChart: updateGroundingCurrentText(data.fGroundingCurrentValue, PHASE_A)
            BJChart->>BJChart: m_data.fGroundingCurrentPhaseAValue = value
        else B相选中
            BJChart->>BJChart: updateGroundingCurrentText(data.fGroundingCurrentValue, PHASE_B)
            BJChart->>BJChart: m_data.fGroundingCurrentPhaseBValue = value
        else C相选中
            BJChart->>BJChart: updateGroundingCurrentText(data.fGroundingCurrentValue, PHASE_C)
            BJChart->>BJChart: m_data.fGroundingCurrentPhaseCValue = value
        else N相选中
            BJChart->>BJChart: updateGroundingCurrentText(data.fGroundingCurrentValue, PHASE_N)
            BJChart->>BJChart: m_data.fGroundingCurrentPhaseNValue = value
        end
    else 显示负载电流模式
        BJChart->>BJChart: updateLoadCurrentText(data.fLoadCurrentValue)
        BJChart->>BJChart: m_data.fLoadCurrentValue = value
        Note right of BJChart: 负载电流不区分相别
    end

    BJChart->>BJChart: 计算接地/负载比值
    BJChart->>BJChart: updateGroundingToLoadRatioText(ratio)
    
    alt 比值超过20%阈值
        BJChart->>BJChart: 显示比值报警(红色)
    else 比值正常
        BJChart->>BJChart: 显示正常状态(绿色)
    end
```

## 3. 关键节点详细说明

### 3.1 配置加载节点

**时机**: 视图初始化时
**处理逻辑**:
```cpp
void CurrentDetectionView::loadCurrentDisplayTypeConfig()
{
    // 1. 获取配置管理器实例
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    
    // 2. 进入电流检测配置组
    pConfig->beginGroup(Module::GROUP_CURRENT_DETECTION);
    
    // 3. 读取显示类型配置，提供默认值
    int iDisplayType = pConfig->value(CurrentDetection::KEY_CURRENT_DISPLAY_TYPE, 
                                     CurrentDetection::CURRENT_DISPLAY_DEFAULT).toInt();
    
    // 4. 验证配置值有效性
    if (iDisplayType >= CurrentDetection::CURRENT_DISPLAY_MIN && 
        iDisplayType <= CurrentDetection::CURRENT_DISPLAY_MAX)
    {
        m_eCurrentDisplayType = static_cast<CurrentDetection::CurrentDisplayType>(iDisplayType);
    }
    else
    {
        m_eCurrentDisplayType = CurrentDetection::CURRENT_DISPLAY_DEFAULT;
    }
    
    // 5. 设置按钮初始状态
    updateButtonState();
    
    pConfig->endGroup();
}
```

### 3.2 按钮切换节点

**时机**: 用户点击切换按钮时
**数据变化过程**:

1. **按钮状态变化**: `ButtonBar` 内部状态从当前值切换到新值
2. **事件触发**: 触发 `onButtonValueChanged` 事件
3. **参数验证**: 检查新值的有效性和是否真正发生变化
4. **状态更新**: 更新视图和图表的内部状态变量
5. **显示更新**: 清空历史数据，更新标签显示
6. **配置保存**: 将新状态持久化到配置文件

### 3.3 数据处理节点

**时机**: 每次接收到新的电流数据时
**处理流程**:

```cpp
void CurrentDetectionChart::addSample(const CurrentDetection::CurrentDetectionData &data)
{
    float fDisplayValue = 0.0f;
    
    // 1. 根据当前显示类型选择数据源
    switch (m_eCurrentDisplayType)
    {
    case CurrentDetection::CURRENT_DISPLAY_LOAD:
        fDisplayValue = data.fLoadCurrentValue;      // 使用负载电流
        break;
    case CurrentDetection::CURRENT_DISPLAY_GROUNDING:
        fDisplayValue = data.fGroundingCurrentValue; // 使用接地电流
        break;
    }
    
    // 2. 更新图表显示
    updateCurrentChart(fDisplayValue, getMaxData());
    
    // 3. 更新最大值记录
    if (fDisplayValue > m_fMaxCurrent)
    {
        m_fMaxCurrent = fDisplayValue;
    }
    
    // 4. 更新类型标签
    updateCurrentTypeLabel();
    
    // 5. 报警检查(仅接地电流)
    if (m_eCurrentDisplayType == CurrentDetection::CURRENT_DISPLAY_GROUNDING)
    {
        checkGroundingCurrentAlarm(fDisplayValue);
    }
}
```

### 3.4 配置保存节点

**时机**: 切换操作完成后
**保存内容**:
- 当前显示类型枚举值
- 时间戳(用于调试)
- 用户ID(如果需要)

## 4. 状态变化矩阵

| 当前状态 | 用户操作 | 新状态 | 数据源变化 | 界面变化 |
|---------|---------|--------|-----------|---------|
| 负载电流显示 | 点击切换按钮 | 接地电流显示 | fLoadCurrent → fGroundCurrent | 标签更新、历史清空 |
| 接地电流显示 | 点击切换按钮 | 负载电流显示 | fGroundCurrent → fLoadCurrent | 标签更新、历史清空 |
| 负载电流显示 | 数据更新 | 负载电流显示 | 继续使用fLoadCurrent | 仪表盘更新 |
| 接地电流显示 | 数据更新 | 接地电流显示 | 继续使用fGroundCurrent | 仪表盘更新+报警检查 |

## 5. 错误处理时序

```mermaid
sequenceDiagram
    participant User as 用户
    participant View as CurrentDetectionView
    participant Chart as CurrentDetectionChart
    participant Config as ConfigManager
    participant Log as 日志系统

    User->>View: 点击切换按钮
    View->>View: onButtonValueChanged(id, invalidValue)
    
    alt 参数验证失败
        View->>Log: logError("Invalid parameter")
        View->>View: 保持当前状态不变
        View-->>User: 无响应(静默处理)
    else 图表组件为空
        View->>Log: logError("Chart component is null")
        View->>View: 尝试重新创建图表
        alt 重新创建成功
            View->>Chart: setCurrentDisplayType(newType)
        else 重新创建失败
            View->>Log: logError("Failed to recreate chart")
            View-->>User: 显示错误提示
        end
    else 配置保存失败
        View->>Chart: setCurrentDisplayType(newType) // 先更新显示
        View->>Config: saveCurrentDisplayTypeConfig()
        Config-->>View: 保存失败
        View->>Log: logWarning("Config save failed, will retry")
        View->>View: 标记需要重试保存
    end
```

## 6. 性能优化考虑

### 6.1 数据处理优化
- **避免重复计算**: 切换时只改变数据源，不重新计算历史数据
- **内存管理**: 清空历史数据时保留最大值，减少内存占用
- **响应速度**: 切换操作在主线程中同步完成，确保即时响应

### 6.2 界面更新优化
- **最小化重绘**: 只更新必要的界面元素
- **批量更新**: 将多个界面更新操作合并执行
- **异步保存**: 配置保存操作可以异步执行，不阻塞界面

## 7. 调试和监控

### 7.1 关键日志点
1. **配置加载**: 记录加载的配置值和默认值使用情况
2. **切换操作**: 记录每次切换的源状态和目标状态
3. **数据处理**: 记录数据源切换和处理结果
4. **错误处理**: 记录所有异常情况和恢复操作

### 7.2 性能监控
- **切换响应时间**: 从用户点击到界面更新完成的时间
- **数据处理延迟**: 从数据到达到显示更新的时间
- **内存使用**: 监控历史数据清理的内存释放效果

这个详细的时序图说明文档为开发人员提供了完整的按钮切换数据变化过程的技术参考，有助于理解系统行为和进行问题诊断。
