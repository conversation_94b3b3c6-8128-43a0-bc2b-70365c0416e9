---
description: 
globs: 
alwaysApply: false
---
# Report System Structure

This document describes the structure and organization of the daily report system.

## Directory Structure

The report system follows this directory structure:

```
├── archived_reports/
│   ├── daily_report_2025_01.md
│   ├── daily_report_2025_02.md
│   ├── daily_report_2025_03.md
│   ├── daily_report_2025_04.md
│   ├── monthly_summary_2025_01.md
│   ├── monthly_summary_2025_02.md
│   └── weekly_report.md
├── config/
│   └── get_shanghai_time.py
└── File/
    └── YYYYMMDD/
        └── (Daily work documents)
```

## Key Files

- [archived_reports/daily_report_2025_04.md](mdc:archived_reports/daily_report_2025_04.md) - Current month's daily report file
- [config/get_shanghai_time.py](mdc:config/get_shanghai_time.py) - Script to get current Shanghai time

## Report Lifecycle

1. **Daily Reports**:
   - Created in `archived_reports/daily_report_YYYY_MM.md`
   - Each day's entry is appended to the current month's file
   - Format follows the standard described in [.cursor/rules/daily-report-format.mdc](mdc:.cursor/rules/daily-report-format.mdc)

2. **Supporting Documents**:
   - Stored in `File/YYYYMMDD/` folders
   - Named according to the content type
   - Referenced in daily reports when relevant

3. **Monthly Archiving**:
   - At the beginning of each month, a new daily report file is created
   - Previous month's report remains in the archived_reports directory

## Daily Report Workflow

1. Create daily work documents in the appropriate `File/YYYYMMDD/` folder
2. Update the daily report in `archived_reports/daily_report_YYYY_MM.md`
3. Add work entries following the required format
4. Include appropriate work tags as described in [.cursor/rules/work-tags.mdc](mdc:.cursor/rules/work-tags.mdc)
5. Complete the "Today's Work Summary" section with time statistics and core achievements

## Report Content Guidelines

1. Focus on technical details and measurable outcomes
2. Use objective language and avoid subjective evaluations
3. Include specific metrics and data points when available
4. Detail analysis processes and solution implementations

