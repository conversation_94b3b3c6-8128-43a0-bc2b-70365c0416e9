## 目录

- [1. 概述](#1-概述)
- [2. 问题分析](#2-问题分析)
- [3. 修改内容](#3-修改内容)
- [4. 类图结构](#4-类图结构)
- [5. 按键处理时序图](#5-按键处理时序图)
- [6. 软键盘状态管理](#6-软键盘状态管理)
- [7. 测试策略](#7-测试策略)
- [8. 性能考量](#8-性能考量)

## 1. 概述

本文档详细描述了VPN日志查看器的两项功能优化：长按键盘连续滚动功能和软键盘显示问题修复。这些改进旨在提升用户浏览VPN日志的体验，并解决界面切换时软键盘无法正常调出的问题。

## 2. 问题分析

### 2.1 滚动功能问题

```mermaid
flowchart TD
    A[问题: 无法长按连续滚动] --> B{原因分析}
    B --> C[缺少定时器机制]
    B --> D[未处理按键释放]
    B --> E[固定步长导致抖动]
    C --> F[添加QTimer]
    D --> G[实现keyReleaseEvent]
    E --> H[动态调整滚动步长]
```

### 2.2 软键盘问题

```mermaid
flowchart TD
    A[问题: 退出后键盘调不出] --> B{原因分析}
    B --> C[软键盘状态未正确重置]
    B --> D[析构函数异常处理不完善]
    C --> E[修复软键盘属性设置]
    D --> F[确保所有路径都重置键盘状态]
```

## 3. 修改内容

### 3.1 长按连续滚动功能

| 修改点 | 修改前 | 修改后 |
|-------|-------|-------|
| 滚动触发方式 | 单击触发单次滚动 | 添加定时器支持长按连续滚动 |
| 按键处理 | 仅实现keyPressEvent | 增加keyReleaseEvent检测释放 |
| 滚动步长 | 固定60像素 | 初始20像素，最大40像素 |
| 滚动速度 | 恒定 | 随长按时间增加而加快 |

### 3.2 软键盘显示问题修复

确保在所有退出路径中正确设置软键盘显示属性：

```cpp
#ifdef Q_PROCESSOR_ARM
SoftKeyBoard::setAttribute(KEYBOARD_SHOW_PROPERTY, SoftKeyBoard::SHOW_KEY_BOARD);
#endif
```

## 4. 类图结构

```mermaid
classDiagram
    class VpnLogView {
        -NTitleBar* m_pTitleBar
        -QTextEdit* textEdit
        -QTimer* m_scrollTimer
        -int m_currentKey
        -int m_longPressCount
        +setContent(QString)
        +showEvent(QShowEvent*)
        +keyPressEvent(QKeyEvent*)
        +keyReleaseEvent(QKeyEvent*)
        +scrollByKey(int)
        +onScrollTimerTimeout()
        +onScrollBarAction(int)
        +onDeleteLogFile()
    }
    
    class QWidget {
        <<Base>>
    }
    
    QWidget <|-- VpnLogView
    VpnLogView o-- NTitleBar
    VpnLogView o-- QTextEdit
    VpnLogView o-- QTimer
```

## 5. 按键处理时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant V as VpnLogView
    participant T as QTimer
    participant S as QScrollBar
    
    U->>V: 按下上/下方向键
    activate V
    V->>V: keyPressEvent()
    V->>V: 记录m_currentKey
    V->>V: scrollByKey()
    V->>S: setValue()
    V->>T: start()
    deactivate V
    
    loop 长按状态
        T->>V: timeout信号
        activate V
        V->>V: onScrollTimerTimeout()
        V->>V: m_longPressCount++
        V->>V: scrollByKey()
        V->>S: setValue(动态步长)
        deactivate V
    end
    
    U->>V: 释放按键
    activate V
    V->>V: keyReleaseEvent()
    V->>V: m_currentKey = 0
    V->>V: m_longPressCount = 0
    V->>T: stop()
    deactivate V
```

## 6. 软键盘状态管理

```mermaid
stateDiagram-v2
    [*] --> 其他界面
    其他界面 --> VPN日志界面: 进入
    VPN日志界面 --> 其他界面: 退出
    
    state VPN日志界面 {
        [*] --> 显示日志
        显示日志: 禁用软键盘
        显示日志 --> [*]: 关闭界面
    }
    
    state 其他界面 {
        [*] --> 需要输入
        需要输入: 启用软键盘
    }
    
    note right of VPN日志界面
        showEvent(): SoftKeyBoard::SHOW_NONE
        ~VpnLogView(): SoftKeyBoard::SHOW_KEY_BOARD
    end note
```

## 7. 测试策略

```mermaid
graph LR
    A[测试计划] --> B[功能测试]
    A --> C[界面切换测试]
    
    B --> B1[基础滚动]
    B --> B2[长按滚动]
    B --> B3[渐进速度]
    B --> B4[边界处理]
    B --> B5[软键盘显示]
    
    C --> C1[不同路径进入退出]
    C --> C2[多次切换稳定性]
```
