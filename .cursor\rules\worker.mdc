---
description: 
globs: 
alwaysApply: true
---
## 日报风格和规则

- 详细描述每项任务的原因、过程、结果和后续计划。
- 使用清晰的条目和子条目结构，确保信息的条理性和可读性。
- 包含具体的技术细节和分析过程，以便于后续参考和学习。
- 强调团队协作和沟通，记录与同事的讨论和反馈。
- 避免使用"确保"等过于官方的用语，保持语言简洁明了。
- 内容描述必须客观具体，禁止使用主观评价词：
  - 禁止使用"提升用户体验"、"优化"、"增强"、"提升"等主观评价词
  - 使用"完善代码功能"、"完善代码健壮性"等具体的技术描述
  - 保留具体的技术实现细节
  - 使用"实现"、"完成"、"修复"等客观描述词
  - 避免使用"更好"、"更快"、"更强"等比较级形容词
  - 使用具体的数据和指标替代主观评价

  ## 系统规则

- 数据准确性：确保输入数据的准确性和及时性。
- 定期更新：用户需定期更新日报、周报和月报。
- 数据安全：遵循公司数据安全和隐私政策。
- 内容不得空洞，必须具体且符合日常工作的实际情况。
- `File`文件夹采用分层结构：年份/月份/日期，例如`File/2025/04/20250401/`。生成日报前需读取对应日期文件夹下的文件，以便更准确地了解当前工作。
- `archived_reports`文件夹采用分层结构：年份/报告类型/文件，例如`archived_reports/2025/daily/daily_report_2025_04.md`。所有报告按年份和类型归类存放。
- 所有报告类文件必须遵循`archived_reports/file_structure_guideline.md`中定义的命名和存放规则，确保文件结构统一清晰。
- 年度交替时，需创建新的年度目录结构，包括daily、weekly和monthly三个子目录。
- 在生成日报时，自动运行Python脚本以获取当前的本地上海时间，无需用户操作介入。
- 在`config`文件夹下创建Python文件`get_shanghai_time.py`，用于获取当前的本地上海时间。
- 在日报文件中，自动获取当前的本地上海时间，并将其作为文件的日期。
- 在生成钉钉日报时，自动按照指定格式生成，包括任务、分析过程、问题发现和解决方案。
- 在生成日报时，按照带有工作标签（设计、编码、维护、管理、学习）的格式生成。
- 每个月初创建一个新的日报文件，将上个月的内容归档到对应年份和类型的目录中。
- 每次用户询问当前日期时，自动运行 `config/get_shanghai_time.py` 脚本以获取当前的上海时间。
- 工作标签格式必须使用方括号[]，例如[开发]、[测试]、[维护]、[管理]、[学习]、[沟通]、[支持]等，禁止使用【】或其他格式的标签。

## 数据输入

- 每日输入：用户每天输入当天的工作内容和所用工时。
- 每周输入：用户每周总结本周的工作进展。
- 每月输入：用户每月总结本月的工作成果。

## 数据处理

- 日报处理：根据当前日期，读取`File/YYYY/MM/YYYYMMDD/`目录下的文档内容，自动获取上海时间，将每日的工作内容和工时按照指定格式（包含工作标签）记录到`archived_reports/YYYY/daily/daily_report_YYYY_MM.md`中。
- 周报处理：每周自动汇总本周的日报内容，生成周报并记录到`archived_reports/YYYY/weekly/weekly_report_YYYY_MM.md`。
- 月报处理：每月自动汇总本月的周报内容，生成月报并记录到`archived_reports/YYYY/monthly/monthly_report_YYYY_MM.md`。分析哪个项目耗时最多。

## 数据输出

- 日报输出：提供每日的工作记录。
- 周报输出：提供每周的工作总结。
- 月报输出：提供每月的工作总结和耗时分析。
