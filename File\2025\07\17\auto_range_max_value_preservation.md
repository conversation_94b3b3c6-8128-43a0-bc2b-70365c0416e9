# 自动档位切换时保留最大值功能实现

## 📋 需求说明

**目标**: 自动档位切换时保留最大值，手动档位切换时清空最大值

**用户体验**:
- 自动模式下，用户可以看到在不同档位测量过程中的历史最大值
- 手动切换档位时，清空最大值以避免不同档位数据混淆

## 🔍 问题分析

### 修改前的问题
```cpp
// 自动切换档位时
void CurrentDetectionChart::switchToRange(CurrentDetection::CurrentRangeGear eNewRange)
{
    // ...
    clear();  // ❌ 问题：清空了最大值
}

// 手动切换档位时  
void CurrentDetectionChart::setCurrentRangeGear(CurrentDetection::CurrentRangeGear eCurrentRangeGear)
{
    // ...
    clear();  // ✅ 正确：应该清空最大值
}
```

### 根本原因
自动切换和手动切换都使用了相同的 `clear()` 函数，无法区分处理。

## 🔧 解决方案

### 1. 新增函数：clearHistoryDataOnly()

```cpp
/*************************************************************
 * 功能：只清空历史数据，保留最大值（用于自动档位切换）
 ************************************************************/
void CurrentDetectionChart::clearHistoryDataOnly()
{
    m_currentDatas.clear();           // 清空历史数据数组
    // 保留 m_fMaxCurrent 不变      // ✅ 保留最大值
    // 保留 m_pMaxValueLabel 显示不变 // ✅ 保留最大值显示
    m_pGaugeArc->setValue(m_fRangeMin); // 重置仪表盘指针到最小值
}
```

### 2. 修改自动切换逻辑

```cpp
/*************************************************************
 * 功能：切换到指定量程（自动模式）
 ************************************************************/
void CurrentDetectionChart::switchToRange(CurrentDetection::CurrentRangeGear eNewRange)
{
    if (eNewRange != m_eActualRangeGear) {
        logDebug(QString("Auto range switch: %1 -> %2, current value: %3")
                 .arg(m_eActualRangeGear).arg(eNewRange).arg(m_fLastSwitchValue));
        
        m_eActualRangeGear = eNewRange;
        updateRange();  // 更新仪表盘量程
        clearHistoryDataOnly();  // ✅ 只清空历史数据，保留最大值
    }
}
```

### 3. 保持手动切换逻辑不变

```cpp
/*************************************************************
 * 功能：设置电流测试类型（手动模式）
 ************************************************************/
void CurrentDetectionChart::setCurrentRangeGear(CurrentDetection::CurrentRangeGear eCurrentRangeGear)
{
    if (eCurrentRangeGear != m_eCurrentRangeGear)
    {
        // ... 档位设置逻辑 ...
        updateRange();
        clear();  // ✅ 手动切换时完全清空，包括最大值
    }
}
```

## 📊 功能对比

### 修改前
| 切换方式 | 历史数据 | 最大值 | 最大值显示 | 仪表盘指针 |
|----------|----------|--------|------------|------------|
| 自动切换 | 清空 ❌ | 清空 ❌ | 清空 ❌ | 重置 ✅ |
| 手动切换 | 清空 ✅ | 清空 ✅ | 清空 ✅ | 重置 ✅ |

### 修改后
| 切换方式 | 历史数据 | 最大值 | 最大值显示 | 仪表盘指针 |
|----------|----------|--------|------------|------------|
| 自动切换 | 清空 ✅ | 保留 ✅ | 保留 ✅ | 重置 ✅ |
| 手动切换 | 清空 ✅ | 清空 ✅ | 清空 ✅ | 重置 ✅ |

## 🎯 使用场景示例

### 场景1: 自动档位切换过程
```
初始状态: 10A档位, 最大值=0A
测量5.5A: 10A档位, 最大值=5.5A
测量12.3A: 自动切换到500A档位, 最大值=12.3A (保留)
测量125.6A: 500A档位, 最大值=125.6A (更新)
测量0.8A: 自动切换到10A档位, 最大值=125.6A (保留)
```

### 场景2: 手动档位切换
```
当前状态: 自动档位, 最大值=125.6A
用户手动切换到10A档位: 最大值=0A (清空)
测量3.2A: 10A档位, 最大值=3.2A (重新开始)
```

## 🧪 测试验证

### 测试用例1: 自动档位切换保留最大值
```
步骤:
1. 选择"自动"档位
2. 开始连续采样
3. 观察随机电流值触发档位切换
4. 验证最大值是否保留

预期结果:
- 档位自动切换时，右下角最大值不会清空
- 最大值会持续更新为历史最大值
```

### 测试用例2: 手动档位切换清空最大值
```
步骤:
1. 在自动模式下测量一段时间，记录最大值
2. 手动切换到其他档位（如10A、500A、5000A）
3. 观察最大值是否被清空

预期结果:
- 手动切换档位时，最大值立即清空为0
- 重新开始记录新的最大值
```

### 测试用例3: 混合模式测试
```
步骤:
1. 自动模式测量，记录最大值A
2. 手动切换档位，最大值清空
3. 再次切换到自动模式
4. 观察最大值记录

预期结果:
- 自动→手动：最大值清空
- 手动→自动：从0开始重新记录
- 自动模式内部切换：最大值保留
```

## 🔄 兼容性分析

### 不受影响的功能
- ✅ 仪表盘指针位置计算
- ✅ 电流值显示和格式化
- ✅ 档位切换逻辑
- ✅ 数据采样和存储

### 改进的功能
- ✅ 自动模式下更好的用户体验
- ✅ 保留有价值的历史最大值信息
- ✅ 区分自动和手动操作的不同需求

### 潜在风险
- ⚠️ 用户可能对跨档位的最大值产生困惑
- ⚠️ 需要在界面上明确说明最大值的含义

## 💡 用户界面建议

### 可选的界面改进
1. **最大值标签增强**:
   ```
   当前显示: "125.6A"
   建议显示: "最大值: 125.6A (历史)"
   ```

2. **档位切换提示**:
   ```
   自动切换时: 不显示提示，保持最大值
   手动切换时: 短暂显示"最大值已重置"
   ```

3. **清空按钮**:
   ```
   添加独立的"清空最大值"按钮，让用户主动控制
   ```

## ✅ 实现状态

- [x] 添加 `clearHistoryDataOnly()` 函数
- [x] 修改 `switchToRange()` 使用新的清空函数
- [x] 保持 `setCurrentRangeGear()` 原有逻辑
- [x] 头文件声明更新
- [x] 代码注释完善
- [ ] 用户界面测试验证（待测试）

## 🎉 总结

通过这个修改：

1. **自动档位切换**: 保留最大值，提供更好的用户体验
2. **手动档位切换**: 清空最大值，避免数据混淆
3. **代码结构**: 清晰分离不同场景的处理逻辑
4. **向后兼容**: 不影响现有的手动操作习惯

现在用户在自动模式下可以看到完整的历史最大值，而手动切换档位时仍然会清空最大值以保持数据的准确性！
