# 负载/接地切换按钮开发 - 今日工作计划

**日期**: 2025-01-18  
**项目**: T95电流检测模块 - 负载/接地切换按钮功能开发  
**预计总工时**: 6.5小时  

## 📋 工作任务清单

### 🔧 阶段一：基础架构搭建 (1.5小时)

#### ✅ 任务1: 基础数据结构实现 (30分钟)
**文件修改**:
- `Z200\module\currentdetection\currentdetectiondefine.h`
- `Z200\view\currentdetection\currentdetectionviewdefine.h`  
- `Z200\module\currentdetection\currentdetectionconfig.h`

**具体工作**:
```cpp
// 1. 在currentdetectiondefine.h中添加枚举
enum CurrentDisplayType
{
    CURRENT_DISPLAY_LOAD = 0,
    CURRENT_DISPLAY_GROUNDING,
    CURRENT_DISPLAY_MIN = CURRENT_DISPLAY_LOAD,
    CURRENT_DISPLAY_MAX = CURRENT_DISPLAY_GROUNDING,
    CURRENT_DISPLAY_DEFAULT = CURRENT_DISPLAY_LOAD,
};

// 2. 在currentdetectionviewdefine.h中添加文本定义
const char* const TEXT_CURRENT_DISPLAY_TYPE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Current Type");
const char* const TEXT_LOAD_CURRENT_DISPLAY = QT_TRANSLATE_NOOP("CurrentDetectionView", "Load Current");
const char* const TEXT_GROUNDING_CURRENT_DISPLAY = QT_TRANSLATE_NOOP("CurrentDetectionView", "Grounding Current");

// 3. 在currentdetectionconfig.h中添加配置项
KEY_CURRENT_DISPLAY_TYPE,
```

#### ✅ 任务2: CurrentDetectionView按钮集成 (45分钟)
**文件修改**:
- `Z200\view\currentdetection\currentdetectionview.h`
- `Z200\view\currentdetection\currentdetectionview.cpp`

**具体工作**:
- 修改按钮枚举，添加`BUTTON_CURRENT_DISPLAY_TYPE`
- 创建`s_CurrentDisplayTypeCfg`配置结构
- 更新`s_CurrentDetectionButtonInfo`数组
- 添加成员变量`m_eCurrentDisplayType`

#### ✅ 任务3: BJCurrentDetectionView按钮集成 (30分钟)
**文件修改**:
- `Z200\view\currentdetection\bjcurrentdetectionview.h`
- `Z200\view\currentdetection\bjcurrentdetectionview.cpp`

**具体工作**:
- 修改按钮枚举，添加`BUTTON_CURRENT_DISPLAY_TYPE`
- 更新按钮信息数组配置
- 添加成员变量`m_eCurrentDisplayType`

### 🎯 阶段二：核心功能实现 (2.5小时)

#### ✅ 任务4: 视图类成员变量和方法实现 (60分钟)
**具体工作**:
```cpp
// 1. 添加成员变量
CurrentDetection::CurrentDisplayType m_eCurrentDisplayType;

// 2. 实现配置管理方法
void loadCurrentDisplayTypeConfig();
void saveCurrentDisplayTypeConfig();

// 3. 在构造函数中调用loadCurrentDisplayTypeConfig()
// 4. 在析构函数中确保配置已保存
```

#### ✅ 任务5: 事件处理逻辑实现 (45分钟)
**具体工作**:
```cpp
// 在onButtonValueChanged中添加处理逻辑
case BUTTON_CURRENT_DISPLAY_TYPE:
{
    // 参数验证
    // 状态更新
    // 图表通知
    // 配置保存
    // 错误处理
}
```

#### ✅ 任务6: 图表组件接口扩展 (40分钟)
**文件修改**:
- `Z200\view\currentdetection\currentdetectionchart.h`
- `Z200\view\currentdetection\currentdetectionchart.cpp`
- `Z200\view\currentdetection\bjcurrentdetectionchart.h`
- `Z200\view\currentdetection\bjcurrentdetectionchart.cpp`

**具体工作**:
```cpp
// 1. 添加接口方法
void setCurrentDisplayType(CurrentDetection::CurrentDisplayType eDisplayType);
CurrentDetection::CurrentDisplayType getCurrentDisplayType() const;

// 2. 添加成员变量
CurrentDetection::CurrentDisplayType m_eCurrentDisplayType;
QLabel* m_pCurrentTypeLabel;

// 3. 实现updateCurrentTypeLabel()方法
```

### 🔄 阶段三：数据处理优化 (50分钟)

#### ✅ 任务7: 数据处理逻辑修改 (50分钟)
**具体工作**:
```cpp
// 修改addSample方法
void CurrentDetectionChart::addSample(const CurrentDetection::CurrentDetectionData &data)
{
    float fDisplayValue = 0.0f;
    
    switch (m_eCurrentDisplayType)
    {
    case CurrentDetection::CURRENT_DISPLAY_LOAD:
        fDisplayValue = data.fLoadCurrentValue;
        break;
    case CurrentDetection::CURRENT_DISPLAY_GROUNDING:
        fDisplayValue = data.fGroundingCurrentValue;
        break;
    }
    
    updateCurrentChart(fDisplayValue);
    updateCurrentTypeLabel();
}
```

### 🧪 阶段四：测试验证 (2小时)

#### ✅ 任务8: 编译测试和问题修复 (60分钟)
**具体工作**:
- 编译项目，修复语法错误
- 解决链接错误和依赖问题
- 修复头文件包含问题
- 确保代码能够正常编译通过

#### ✅ 任务9: 功能测试验证 (45分钟)
**测试用例**:
1. **按钮切换测试**:
   - 启动电流检测界面
   - 点击切换按钮，验证显示类型变化
   - 验证按钮状态正确更新

2. **数据显示测试**:
   - 在负载电流模式下，验证显示负载电流数据
   - 切换到接地电流模式，验证显示接地电流数据
   - 验证数据值的正确性

3. **配置持久化测试**:
   - 切换显示类型并保存
   - 重启应用，验证配置恢复正确
   - 测试默认配置的加载

4. **报警功能测试**:
   - 在接地电流模式下，测试报警阈值功能
   - 验证报警状态显示正确

#### ✅ 任务10: 代码审查和文档更新 (30分钟)
**具体工作**:
- 代码自查，确保符合编码规范
- 检查注释完整性和准确性
- 更新相关技术文档
- 记录开发过程中的问题和解决方案

## 📊 时间分配

| 阶段 | 任务数 | 预计时间 | 关键里程碑 |
|------|--------|----------|------------|
| 基础架构搭建 | 3 | 1.5小时 | 数据结构和按钮配置完成 |
| 核心功能实现 | 3 | 2.5小时 | 事件处理和图表接口完成 |
| 数据处理优化 | 1 | 0.5小时 | 数据切换逻辑完成 |
| 测试验证 | 2 | 2小时 | 功能测试通过 |
| **总计** | **9** | **6.5小时** | **功能开发完成** |

## 🎯 关键检查点

### 上午检查点 (11:00)
- [ ] 基础数据结构定义完成
- [ ] 两个视图类的按钮集成完成
- [ ] 编译无语法错误

### 下午检查点 (15:00)
- [ ] 事件处理逻辑实现完成
- [ ] 图表组件接口扩展完成
- [ ] 数据处理逻辑修改完成

### 晚间检查点 (17:30)
- [ ] 编译测试通过
- [ ] 基本功能测试验证完成
- [ ] 代码审查完成

## ⚠️ 风险点和应对策略

### 风险1: 编译错误
**可能原因**: 头文件依赖、命名空间冲突  
**应对策略**: 逐步编译，及时修复错误

### 风险2: 按钮配置问题
**可能原因**: 按钮数组配置错误、图标资源缺失  
**应对策略**: 参考现有按钮配置，使用占位图标

### 风险3: 数据显示异常
**可能原因**: 数据源选择逻辑错误  
**应对策略**: 添加调试日志，逐步验证数据流

### 风险4: 配置保存失败
**可能原因**: 配置文件权限、路径问题  
**应对策略**: 添加异常处理，提供默认值

## 📝 开发注意事项

1. **代码规范**: 严格遵循现有代码的命名规范和注释格式
2. **向后兼容**: 确保新功能不影响现有功能
3. **错误处理**: 添加完善的参数验证和异常处理
4. **性能考虑**: 避免频繁的配置读写操作
5. **调试支持**: 添加关键节点的调试日志

## 🔄 明日计划预览

1. **高级功能优化**: 自动量程切换优化
2. **用户体验改进**: 切换动画效果
3. **单元测试编写**: 完整的测试用例覆盖
4. **性能测试**: 大数据量下的性能验证
5. **文档完善**: 用户手册更新

---

**开发者**: [您的姓名]  
**审核者**: [审核者姓名]  
**创建时间**: 2025-01-18 09:00  
**预计完成时间**: 2025-01-18 17:30
