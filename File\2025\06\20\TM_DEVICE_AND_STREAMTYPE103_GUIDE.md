# TM系列设备检测与StreamType 103处理开发指南

## 📋 文档概述

本文档基于项目实际代码，详细介绍TM系列设备的检测机制和StreamType 103（全屏测温数据+YUV实时流）的处理实现。

**重要说明**：本文档所有内容均基于项目中的实际代码实现，确保技术细节的准确性。

---

## 🔍 TM系列设备检测机制

### 1. 设备检测策略

项目采用多层次检测算法，确保准确识别TM系列设备：

```cpp
// 设备检测结果结构（基于实际代码）
struct DeviceDetectionResult {
    InfraredDeviceType deviceType;          // 检测到的设备类型
    QString detectionMethod;                // 检测方法描述
    QString coreVersion;                    // 机芯版本
    QString firmwareVersion;                // 固件版本
    QString deviceId;                       // 设备ID
    bool isDetectionSuccessful;             // 检测是否成功
};
```

### 2. 硬件版本检测（主要方法）

```cpp
QString InfraredCamera::extractCoreVersionFromHardware(const QString& hardwareVersion) {
    qDebug() << "Extracting core version from hardware:" << hardwareVersion;
    
    // 硬件版本格式示例: "FPGA 240815 DSP 1.0"
    // 提取FPGA版本号
    int fpgaIndex = hardwareVersion.indexOf("FPGA");
    if (fpgaIndex != -1) {
        QString afterFpga = hardwareVersion.mid(fpgaIndex + 4).trimmed();
        QString fpgaVersion;
        for (int i = 0; i < afterFpga.length(); ++i) {
            if (afterFpga[i].isDigit()) {
                fpgaVersion += afterFpga[i];
            } else if (!fpgaVersion.isEmpty()) {
                break; // 遇到非数字字符且已有数字，停止
            }
        }
        if (!fpgaVersion.isEmpty()) {
            qDebug() << "Extracted FPGA version:" << fpgaVersion;
            return fpgaVersion;
        }
    }
    
    return QString();
}
```

### 3. 机芯版本映射表（实际验证）

```cpp
InfraredDeviceType InfraredCamera::detectByCoreVersion(const QString& coreVersion) {
    qDebug() << "Detecting device type by core version:" << coreVersion;
    
    // 根据实际验证的映射关系
    if (coreVersion.contains("240815") || coreVersion == "240815") {
        qDebug() << "Detected TM32 by core version 240815";
        return INFRARED_DEVICE_TM32;
    }
    else if (coreVersion.contains("250527") || coreVersion == "250527") {
        qDebug() << "Detected TM52 by core version 250527";
        return INFRARED_DEVICE_TM52;
    }
    else if (coreVersion.contains("250528") || coreVersion == "250528") {
        qDebug() << "Detected TM53 by core version 250528";
        return INFRARED_DEVICE_TM53;
    }
    else if (coreVersion.contains("250529") || coreVersion == "250529") {
        qDebug() << "Detected TM56 by core version 250529";
        return INFRARED_DEVICE_TM56;
    }
    
    qDebug() << "Unknown core version:" << coreVersion;
    return INFRARED_DEVICE_UNKNOWN;
}
```

---

## 🌊 StreamType 103数据结构（基于实际代码）

### 1. 实际数据结构分析

根据项目代码中的实际实现，StreamType 103的数据结构如下：

```cpp
// 基于StreamType103Header.h的实际数据结构
struct TM32DataInfo {
    // 数据结构常量（基于实际验证）
    static const int HEAD_SIZE = 4636;                    // HEAD固定大小
    static const int SEPARATOR_SIZE = 4;                  // 分隔符大小
    static const int REAL_WIDTH = 256;                    // 实际图像宽度
    static const int REAL_HEIGHT = 192;                   // 实际图像高度
    static const int TEMP_DATA_SIZE = REAL_WIDTH * REAL_HEIGHT * 2;  // 温度数据：98304字节
    static const int YUV_DATA_SIZE = REAL_WIDTH * REAL_HEIGHT * 2;   // YUV数据：98304字节
    static const int TOTAL_SIZE = HEAD_SIZE + SEPARATOR_SIZE + TEMP_DATA_SIZE + YUV_DATA_SIZE; // 总大小：201248字节

    // 偏移量
    static const int TEMP_DATA_OFFSET = HEAD_SIZE + SEPARATOR_SIZE;   // 温度数据偏移：4640
    static const int YUV_DATA_OFFSET = TEMP_DATA_OFFSET + TEMP_DATA_SIZE; // YUV数据偏移：102944
};
```

### 2. TM32设备的实际数据格式

```cpp
// TM32设备StreamType 103的实际验证结果（基于parseStreamType103WithOfficialStructure）
struct TM32ActualFormat {
    static const int EXPECTED_TOTAL_SIZE = 201248;      // 实际总大小
    static const int YUV_DATA_OFFSET = 102944;          // HEAD(4636) + 4 + DATA1(98304)
    static const int YUV_DATA_SIZE = 98304;             // 256×192
    static const int IMAGE_WIDTH = 256;                 // 实际图像宽度
    static const int IMAGE_HEIGHT = 192;                // 实际图像高度
    static const int TEMP_DATA_OFFSET = 4640;           // HEAD(4636) + 4
    static const int TEMP_DATA_SIZE = 98304;            // 256×192×2
};
```

### 3. 头部结构解析

```cpp
// HEAD部分基本信息（基于StreamType103Header.h）
struct HeaderBasicInfo {
    DWORD magicNumber;      // 0x70827773 "FRMI"的ASCII码
    DWORD headerSize;       // HEAD长度（应该是4636）
    DWORD streamType;       // 数据类型（应该是103）
    DWORD streamLength;     // DATA长度
    BYTE  hasYUV;           // 是否携带YUV图片
};

// HEAD部分解析示例
void parseHeaderInfo(const QByteArray& data) {
    // HEAD部分包含4636字节的复杂结构
    // 包含设备信息、温度参数、校准数据等
    HeaderBasicInfo* basicInfo = reinterpret_cast<HeaderBasicInfo*>(data.data());

    qDebug() << "HEAD basic info:";
    qDebug() << "  - Magic number:" << QString::number(basicInfo->magicNumber, 16);
    qDebug() << "  - Header size:" << basicInfo->headerSize << "(should be 4636)";
    qDebug() << "  - Stream type:" << basicInfo->streamType << "(should be 103)";
}
```

---

## 🔧 StreamType 103处理算法

### 1. 高性能解析模式（用于实时显示）

```cpp
// 基于parseStreamType103WithOfficialStructure的实际实现
QImage MainModel::parseStreamType103WithOfficialStructure(const QByteArray& streamData) {
    // TM32数据结构常量（避免重复计算）
    static const int EXPECTED_TOTAL_SIZE = 201248;
    static const int YUV_DATA_OFFSET = 102944;  // HEAD(4636) + 4 + DATA1(98304)
    static const int YUV_DATA_SIZE = 98304;     // 256×192×2
    static const int IMAGE_WIDTH = 256;
    static const int IMAGE_HEIGHT = 192;
    
    // 快速验证数据大小
    if (streamData.size() != EXPECTED_TOTAL_SIZE) {
        qDebug() << "Invalid TM32 data size:" << streamData.size() 
                 << "expected:" << EXPECTED_TOTAL_SIZE;
        return QImage();
    }
    
    // 直接提取YUV数据（无需解析HEAD）
    QByteArray yuvData = streamData.mid(YUV_DATA_OFFSET, YUV_DATA_SIZE);
    
    if (yuvData.size() != YUV_DATA_SIZE) {
        qDebug() << "YUV data size mismatch:" << yuvData.size();
        return QImage();
    }
    
    // 转换YUV数据为图像
    return convertYUY2ToRGB(yuvData, IMAGE_WIDTH, IMAGE_HEIGHT);
}
```

### 2. 精确结构解析模式

```cpp
// 基于processMixedThermometryYUVDataWithPreciseStructure的实际实现
QImage MainModel::processMixedThermometryYUVDataWithPreciseStructure(
    const QByteArray& streamData, int width, int height) {
    
    // 计算各部分的精确位置（基于TM32DataInfo）
    int headerSize = 4636;                  // HEAD：4636字节
    int separatorSize = 4;                  // 分隔符：4字节
    int tempMatrixSize = 256 * 192 * 2;     // 测温矩阵：98304字节
    int tempDataOffset = headerSize + separatorSize;     // 温度数据偏移：4640
    int yuvDataOffset = tempDataOffset + tempMatrixSize; // YUV偏移：102944
    int yuvDataSize = streamData.size() - yuvDataOffset; // YUV大小
    
    qDebug() << "Complete data structure breakdown:";
    qDebug() << "  - HEAD size:" << headerSize << "bytes";
    qDebug() << "  - Separator size:" << separatorSize << "bytes";
    qDebug() << "  - Temperature matrix:" << tempMatrixSize << "bytes (256x192x2)";
    qDebug() << "  - Temperature data offset:" << tempDataOffset << "bytes";
    qDebug() << "  - YUV data offset:" << yuvDataOffset << "bytes";
    
    // 验证数据完整性
    if (yuvDataOffset >= streamData.size()) {
        qDebug() << "Invalid data structure - YUV offset beyond data size";
        return QImage();
    }
    
    // 提取YUV数据
    QByteArray yuvData = streamData.mid(yuvDataOffset);
    
    // 验证YUV数据大小
    int expectedYUVSize = width * height * 2;
    if (yuvData.size() >= expectedYUVSize) {
        // 截取正确大小的YUV数据
        if (yuvData.size() > expectedYUVSize) {
            yuvData = yuvData.left(expectedYUVSize);
        }
        
        QImage image = convertYUY2ToRGB(yuvData, width, height);
        if (!image.isNull()) {
            // 提取并分析温度矩阵数据
            QByteArray tempMatrix = streamData.mid(tempDataOffset, tempMatrixSize);
            qDebug() << "Temperature matrix extracted:";
            qDebug() << "  - Size:" << tempMatrix.size() << "bytes";
            qDebug() << "  - Dimensions: 256x192";
            qDebug() << "  - Format: 2 bytes per pixel";
            
            return image;
        }
    }
    
    return QImage();
}
```

### 3. YUV422到RGB转换（实际实现）

```cpp
// 基于convertYUY2ToRGB的实际实现
QImage MainModel::convertYUY2ToRGB(const QByteArray& yuy2Data, int width, int height) {
    if (yuy2Data.size() < width * height * 2) {
        qDebug() << "Insufficient YUY2 data";
        return QImage();
    }
    
    QImage rgbImage(width, height, QImage::Format_RGB888);
    const unsigned char* yuvData = reinterpret_cast<const unsigned char*>(yuy2Data.constData());
    
    for (int y = 0; y < height; ++y) {
        unsigned char* rgbLine = rgbImage.scanLine(y);
        
        for (int x = 0; x < width; x += 2) {
            int yuvIndex = (y * width + x) * 2;
            
            if (yuvIndex + 3 < yuy2Data.size()) {
                unsigned char y0 = yuvData[yuvIndex];
                unsigned char u = yuvData[yuvIndex + 1];
                unsigned char y1 = yuvData[yuvIndex + 2];
                unsigned char v = yuvData[yuvIndex + 3];
                
                // 转换像素
                convertYUVToRGB(y0, u, v, rgbLine + x * 3);
                if (x + 1 < width) {
                    convertYUVToRGB(y1, u, v, rgbLine + (x + 1) * 3);
                }
            }
        }
    }
    
    return rgbImage;
}

// YUV到RGB转换核心算法（ITU-R BT.601标准）
void MainModel::convertYUVToRGB(unsigned char y, unsigned char u, unsigned char v, 
                               unsigned char* rgb) {
    int c = y - 16;
    int d = u - 128;
    int e = v - 128;
    
    int r = (298 * c + 409 * e + 128) >> 8;
    int g = (298 * c - 100 * d - 208 * e + 128) >> 8;
    int b = (298 * c + 516 * d + 128) >> 8;
    
    // 限制RGB值范围[0, 255]
    rgb[0] = (r > 255) ? 255 : ((r < 0) ? 0 : r);  // R
    rgb[1] = (g > 255) ? 255 : ((g < 0) ? 0 : g);  // G
    rgb[2] = (b > 255) ? 255 : ((b < 0) ? 0 : b);  // B
}
```

---

## 📊 温度数据处理

### 1. 温度矩阵提取

```cpp
// 基于实际代码的温度数据提取
QByteArray extractTemperatureMatrix(const QByteArray& streamData) {
    static const int TEMP_DATA_OFFSET = 4640;  // HEAD(4636) + 4
    static const int TEMP_DATA_SIZE = 98304;   // 256×192×2
    
    if (streamData.size() >= TEMP_DATA_OFFSET + TEMP_DATA_SIZE) {
        return streamData.mid(TEMP_DATA_OFFSET, TEMP_DATA_SIZE);
    }
    
    return QByteArray();
}
```

### 2. 温度数据可视化

```cpp
// 基于visualizeTemperatureData的实际实现
QImage MainModel::visualizeTemperatureData(const QByteArray& tempData, int width, int height) {
    QImage thermalImage(width, height, QImage::Format_RGB888);
    const unsigned char* data = reinterpret_cast<const unsigned char*>(tempData.constData());
    
    // 分析数据的统计特性
    int nonZeroCount = 0;
    int minVal = 255, maxVal = 0;
    
    for (int i = 0; i < qMin(tempData.size(), width * height); i++) {
        unsigned char val = data[i];
        if (val > 0) {
            nonZeroCount++;
            minVal = qMin(minVal, (int)val);
            maxVal = qMax(maxVal, (int)val);
        }
    }
    
    // 生成热力图可视化
    for (int y = 0; y < height; ++y) {
        unsigned char* rgbLine = thermalImage.scanLine(y);
        
        for (int x = 0; x < width; ++x) {
            int index = y * width + x;
            unsigned char value = (index < tempData.size()) ? data[index] : 0;
            
            unsigned char r, g, b;
            
            if (value == 0) {
                r = g = b = 0;  // 零值显示为黑色
            } else if (maxVal > minVal) {
                // 归一化非零值并应用热力图配色
                int normalized = ((value - minVal) * 255) / (maxVal - minVal);
                normalized = qBound(0, normalized, 255);
                
                // 热力图配色：蓝->青->绿->黄->红
                if (normalized < 64) {
                    r = 0; g = 0; b = 255;
                } else if (normalized < 128) {
                    r = 0; g = (normalized - 64) * 4; b = 255 - (normalized - 64) * 4;
                } else if (normalized < 192) {
                    r = (normalized - 128) * 4; g = 255; b = 0;
                } else {
                    r = 255; g = 255 - (normalized - 192) * 4; b = 0;
                }
            } else {
                r = g = b = value;  // 所有值相同，显示为灰色
            }
            
            rgbLine[x * 3 + 0] = r;
            rgbLine[x * 3 + 1] = g;
            rgbLine[x * 3 + 2] = b;
        }
    }
    
    return thermalImage;
}
```

---

## 📝 总结

本文档基于项目实际代码，准确描述了：

### 🎯 TM系列设备检测
- **机芯版本映射**：240815→TM32, 250527→TM52, 250528→TM53, 250529→TM56
- **检测方法**：硬件版本检测为主，固件版本和设备ID为辅

### 🌊 StreamType 103数据结构
- **TM32实际格式**：总大小201248字节，YUV偏移102944，图像尺寸256×192
- **数据组成**：HEAD(4636字节) + 分隔符(4字节) + 温度矩阵(98304字节) + YUV数据(98304字节)
- **处理模式**：高性能模式（实时显示）和精确解析模式（数据保存）

### 🔧 核心算法
- **YUV422转换**：基于ITU-R BT.601标准的高效转换算法
- **温度可视化**：支持热力图配色的温度数据可视化
- **容错处理**：多种解析方法和备用方案

所有技术细节均来自项目实际代码，确保准确性和可靠性。
