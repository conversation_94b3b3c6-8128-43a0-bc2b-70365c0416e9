# 电流检测自动量程切换功能设计文档

## 📋 需求分析

### 功能需求
实现自动量程切换功能，根据实际电流值自动选择合适的档位：

#### 切换规则：
- **当前10A档位，实际值125.146A** → 自动切换到500A档位
- **当前500A档位，实际值650A** → 自动切换到5000A档位  
- **当前任意档位，实际值100mA** → 自动切换到10A档位

#### 新增档位：
- `CURRENT_RANGE_GEAR_AUTO` - 自动档位选项

## 🏗️ 现有代码架构分析

### 1. 枚举定义 (currentdetectiondefine.h)
```cpp
enum CurrentRangeGear
{
    CURRENT_RANGE_GEAR_10A = 0,     // 10A档位
    CURRENT_RANGE_GEAR_500A,        // 500A档位  
    CURRENT_RANGE_GEAR_5000A,       // 5000A档位
    CURRENT_RANGE_GEAR_AUTO,        // 自动档位 (新增)
    
    CURRENT_RANGE_GEAR_MIN = CURRENT_RANGE_GEAR_10A,
    CURRENT_RANGE_GEAR_MAX = CURRENT_RANGE_GEAR_AUTO,
    CURRENT_RANGE_GEAR_DEFAULT = CURRENT_RANGE_GEAR_10A,
};
```

### 2. UI显示文本 (currentdetectionviewdefine.h)
```cpp
const char* const TEXT_RANGE_GEAR_OPTIONS[] =
{
    "10A",
    "500A", 
    "5000A",
    TEXT_AUTO,  // "自动" (已添加)
};
```

### 3. 关键类和函数

#### CurrentDetectionView类
- **配置管理**: `initDatas()`, `saveConfig()`
- **按钮处理**: `onButtonValueChanged()` - 处理量程档位切换
- **数据处理**: `onDataRead()` - 接收电流数据并分发

#### CurrentDetectionChart类  
- **量程设置**: `setCurrentRangeGear()`, `updateRange()`
- **数据处理**: `addSample()` - 处理单个电流样本
- **范围管理**: `setRange()` - 设置仪表盘量程范围

### 4. 数据流分析
```
CurrentDetectionService → CurrentDetectionView::onDataRead() 
    ↓
根据档位选择数据: fGroundingCurrentValue(10A) / fLoadCurrentValue(500A/5000A)
    ↓  
CurrentDetectionChart::addSample() → 仪表盘显示
```

## 🔧 设计方案

### 1. 自动量程切换算法

#### 量程切换阈值设计
```cpp
// 量程切换阈值 (考虑滞回，避免频繁切换)
const float RANGE_SWITCH_THRESHOLD_10A_UP = 8.0f;      // 10A→500A 上切阈值
const float RANGE_SWITCH_THRESHOLD_500A_DOWN = 5.0f;   // 500A→10A 下切阈值  
const float RANGE_SWITCH_THRESHOLD_500A_UP = 400.0f;   // 500A→5000A 上切阈值
const float RANGE_SWITCH_THRESHOLD_5000A_DOWN = 300.0f; // 5000A→500A 下切阈值
```

#### 自动档位选择逻辑
```cpp
CurrentDetection::CurrentRangeGear selectOptimalRange(float fCurrentValue)
{
    if (fCurrentValue <= RANGE_SWITCH_THRESHOLD_10A_UP) {
        return CURRENT_RANGE_GEAR_10A;      // 0-8A使用10A档位
    }
    else if (fCurrentValue <= RANGE_SWITCH_THRESHOLD_500A_UP) {
        return CURRENT_RANGE_GEAR_500A;     // 8-400A使用500A档位
    }
    else {
        return CURRENT_RANGE_GEAR_5000A;    // >400A使用5000A档位
    }
}
```

### 2. 核心修改点

#### 2.1 CurrentDetectionChart类扩展

**新增成员变量**:
```cpp
private:
    bool m_bAutoRangeEnabled;                    // 是否启用自动量程
    CurrentDetection::CurrentRangeGear m_eActualRangeGear;  // 实际使用的档位
    QTimer* m_pRangeSwitchTimer;                 // 防抖定时器
    float m_fLastSwitchValue;                    // 上次切换时的值
```

**新增函数**:
```cpp
public:
    void setAutoRangeEnabled(bool bEnabled);     // 设置自动量程开关
    CurrentDetection::CurrentRangeGear getActualRangeGear() const;  // 获取实际档位

private:
    CurrentDetection::CurrentRangeGear selectOptimalRange(float fCurrentValue);
    void switchToRange(CurrentDetection::CurrentRangeGear eNewRange);
    bool shouldSwitchRange(float fCurrentValue, CurrentDetection::CurrentRangeGear eCurrent);
```

#### 2.2 修改updateRange()函数
```cpp
void CurrentDetectionChart::updateRange()
{
    CurrentDetection::CurrentRangeGear eTargetRange = m_eCurrentRangeGear;
    
    // 如果是自动档位，使用实际档位
    if (CurrentDetection::CURRENT_RANGE_GEAR_AUTO == m_eCurrentRangeGear) {
        eTargetRange = m_eActualRangeGear;
    }
    
    switch (eTargetRange) {
        case CurrentDetection::CURRENT_RANGE_GEAR_10A:
            setRange(0, g_iCurrentRangeGear10A);
            break;
        case CurrentDetection::CURRENT_RANGE_GEAR_500A:
            setRange(0, g_iCurrentRangeGear500A);
            break;
        case CurrentDetection::CURRENT_RANGE_GEAR_5000A:
            setRange(0, g_iCurrentRangeGear5000A);
            break;
    }
}
```

#### 2.3 修改addSample()函数
```cpp
void CurrentDetectionChart::addSample(float fCurrentValue)
{
    // 自动量程切换逻辑
    if (CurrentDetection::CURRENT_RANGE_GEAR_AUTO == m_eCurrentRangeGear) {
        CurrentDetection::CurrentRangeGear eOptimalRange = selectOptimalRange(fCurrentValue);
        if (eOptimalRange != m_eActualRangeGear) {
            switchToRange(eOptimalRange);
        }
    }
    
    // 原有的数据处理逻辑...
    // (超量程处理、数据存储、图表更新等)
}
```

### 3. CurrentDetectionView类修改

#### 3.1 修改onDataRead()函数
```cpp
void CurrentDetectionView::onDataRead(CurrentDetection::CurrentDetectionData data, MultiServiceNS::USERID userId)
{
    if ((getUserId() != INVALID_USER) && (getUserId() == userId)) {
        float fCurrentValue = 0.0f;
        
        // 根据档位或自动模式选择数据源
        if (CurrentDetection::CURRENT_RANGE_GEAR_AUTO == m_eCurrentRangeGear) {
            // 自动模式：选择较大的电流值进行判断
            fCurrentValue = qMax(data.fGroundingCurrentValue, data.fLoadCurrentValue);
        }
        else {
            // 手动模式：按原有逻辑选择
            if (CurrentDetection::CURRENT_RANGE_GEAR_10A == m_eCurrentRangeGear) {
                fCurrentValue = data.fGroundingCurrentValue;
            }
            else {
                fCurrentValue = data.fLoadCurrentValue;
            }
        }
        
        m_pChart->addSample(fCurrentValue);
        
        // 单次采样模式处理
        if(Module::SAMPLEMODE_SINGLE == m_eSampleMode) {
            stopSampleService();
        }
    }
}
```

## 🎯 实现细节

### 1. 防抖机制
```cpp
// 避免频繁切换的防抖逻辑
bool CurrentDetectionChart::shouldSwitchRange(float fCurrentValue, CurrentDetection::CurrentRangeGear eCurrent)
{
    // 滞回比较，避免在阈值附近频繁切换
    switch (eCurrent) {
        case CURRENT_RANGE_GEAR_10A:
            return fCurrentValue > RANGE_SWITCH_THRESHOLD_10A_UP;
        case CURRENT_RANGE_GEAR_500A:
            return (fCurrentValue < RANGE_SWITCH_THRESHOLD_500A_DOWN) || 
                   (fCurrentValue > RANGE_SWITCH_THRESHOLD_500A_UP);
        case CURRENT_RANGE_GEAR_5000A:
            return fCurrentValue < RANGE_SWITCH_THRESHOLD_5000A_DOWN;
    }
    return false;
}
```

### 2. 状态同步
```cpp
void CurrentDetectionChart::switchToRange(CurrentDetection::CurrentRangeGear eNewRange)
{
    if (eNewRange != m_eActualRangeGear) {
        m_eActualRangeGear = eNewRange;
        updateRange();  // 更新仪表盘量程
        clear();        // 清空历史数据
        
        // 发送信号通知UI更新显示
        emit actualRangeChanged(m_eActualRangeGear);
    }
}
```

### 3. UI状态显示
在自动模式下，需要显示当前实际使用的档位：
- 量程按钮显示："自动 (当前: 500A)"
- 或者添加独立的状态显示标签

## 📊 配置管理

### 配置保存
自动档位状态需要保存到配置文件：
```cpp
void CurrentDetectionView::saveConfig()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_CURRENT_DETECTION);
    
    pConfig->setValue(m_eSampleMode, CurrentDetection::KEY_SAMPLE_MODE);
    pConfig->setValue(m_eCurrentRangeGear, CurrentDetection::KEY_RANGE_GEAR);  // 包含AUTO选项
    
    pConfig->endGroup();
}
```

## 🧪 测试用例

### 测试场景1: 向上切换
- 初始状态: 10A档位
- 输入电流: 125.146A  
- 预期结果: 自动切换到500A档位

### 测试场景2: 跨档切换  
- 初始状态: 10A档位
- 输入电流: 650A
- 预期结果: 自动切换到5000A档位

### 测试场景3: 向下切换
- 初始状态: 500A档位  
- 输入电流: 0.1A
- 预期结果: 自动切换到10A档位

### 测试场景4: 防抖测试
- 输入在阈值附近波动的电流值
- 预期结果: 不频繁切换档位

## 🔄 兼容性考虑

1. **向后兼容**: 原有的手动档位选择功能完全保持
2. **配置兼容**: 旧配置文件中没有AUTO选项时，默认使用10A档位  
3. **数据格式**: 保存的数据格式不变，只是档位选择逻辑改变

## 📋 实施计划

### 阶段1: 核心逻辑实现
1. 修改CurrentDetectionChart类，添加自动切换逻辑
2. 实现量程选择算法和防抖机制

### 阶段2: UI集成
1. 修改CurrentDetectionView的数据处理逻辑
2. 更新配置保存和加载

### 阶段3: 测试验证  
1. 单元测试各种切换场景
2. 集成测试UI交互和配置持久化

这个设计既满足了自动量程切换的需求，又保持了代码的可维护性和向后兼容性。
