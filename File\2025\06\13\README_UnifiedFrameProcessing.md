# 统一帧处理架构 (Unified Frame Processing Architecture)

## 概述

本文档描述了红外相机模块的新统一帧处理架构，该架构采用适配器模式重新设计了`InfraredCameraTypes::FrameInfo`，解决了视图层图像显示问题，并提供了更好的可扩展性和性能。

**兼容性说明**: 本架构完全兼容Qt 4.8.7和C++11标准。

## 架构设计

### 核心组件

1. **UnifiedFrameInfo** - 统一的帧信息结构体
2. **IDeviceAdapter** - 设备适配器接口
3. **HikUSBAdapter** - 海康USB设备适配器
4. **FrameProcessor** - 统一帧处理器
5. **AdapterFactory** - 适配器工厂

### 设计模式

- **适配器模式**: 统一不同设备的数据格式
- **工厂模式**: 创建和管理设备适配器
- **观察者模式**: 异步帧处理和事件通知

## 主要改进

### 1. 简化的FrameInfo结构

```cpp
struct UnifiedFrameInfo {
    int width, height;                    // 图像尺寸
    QDateTime timestamp;                  // 时间戳
    DeviceType deviceType;                // 设备类型
    UnifiedImageFormat format;            // 图像格式
    QByteArray rawData;                   // 原始数据
    TemperatureData temperatureData;      // 温度数据
    QVariantMap deviceSpecific;           // 设备特定数据
};
```

### 2. 设备适配器接口

```cpp
class IDeviceAdapter {
public:
    virtual UnifiedFrameInfo processRawFrame(const QByteArray& rawData,
                                           const QVariantMap& metadata) = 0;
    virtual QPixmap convertToDisplayFormat(const UnifiedFrameInfo& frameInfo) = 0;
    virtual TemperatureData extractTemperatureData(const UnifiedFrameInfo& frameInfo) = 0;
};
```

### 3. 统一帧处理器

```cpp
class FrameProcessor {
public:
    bool registerAdapter(DeviceType type, std::shared_ptr<IDeviceAdapter> adapter);
    UnifiedFrameInfo processFrame(const QByteArray& rawData, DeviceType type, const QVariantMap& metadata);
    QPixmap generateDisplayImage(const UnifiedFrameInfo& frameInfo);
};
```

## 使用方法

### 1. 初始化帧处理器（Qt 4.8.7兼容版本）

```cpp
// 创建帧处理器
QSharedPointer<FrameProcessor> processor(new FrameProcessor());

// 注册海康USB适配器
QSharedPointer<IDeviceAdapter> hikAdapter =
    AdapterFactory::createAdapter(DeviceType_HikUSB);
processor->registerAdapter(DeviceType_HikUSB, hikAdapter);

// 启用异步处理
processor->setAsyncProcessingEnabled(true);
```

### 2. 处理帧数据（Qt 4.8.7兼容版本）

```cpp
// 准备元数据
QVariantMap metadata;
metadata["width"] = 640;
metadata["height"] = 480;
metadata["streamType"] = 104; // NV12
metadata["frameRate"] = 25;

// 同步处理
UnifiedFrameInfo frameInfo = processor->processFrame(rawData, DeviceType_HikUSB, metadata);

// 异步处理
processor->processFrameAsync(rawData, DeviceType_HikUSB, metadata);
```

### 3. 生成显示图像

```cpp
// 从统一帧信息生成显示图像
QPixmap displayImage = processor->generateDisplayImage(frameInfo);

// 应用到视图
imageLabel->setPixmap(displayImage);
```

### 4. 在HikInfraredModel中集成

```cpp
void HikInfraredModel::onInfraredFrameDataReceived(const FrameInfo& frameInfo) {
    if (m_useUnifiedFrameProcessing && m_frameProcessor) {
        // 使用新架构
        QVariantMap metadata;
        metadata["width"] = frameInfo.width;
        metadata["height"] = frameInfo.height;
        metadata["streamType"] = frameInfo.streamType;

        m_frameProcessor->processFrameAsync(frameInfo.imageData,
                                          DeviceType::HikUSB,
                                          metadata);
    } else {
        // 传统处理方式
        // ...
    }
}
```

## 性能优化

### 1. 内存池管理

```cpp
class FrameBuffer {
    UnifiedFrameInfo* acquireFrame();
    void releaseFrame(UnifiedFrameInfo* frame);
};
```

### 2. 异步处理

- 支持异步帧处理，避免阻塞主线程
- 使用QtConcurrent进行并行处理
- 智能的线程安全设计

### 3. 缓存优化

- 预分配RGB转换缓冲区
- 重用临时图像对象
- 减少内存分配和拷贝

## 向后兼容性

新架构完全向后兼容，现有代码无需修改：

1. 保留原有的`FrameInfo`结构体
2. 提供`toUnifiedFrameInfo()`转换方法
3. 保持原有信号槽接口
4. 支持传统和新架构的切换

## 扩展新设备

### 1. 创建设备适配器

```cpp
class GuideAdapter : public IDeviceAdapter {
public:
    UnifiedFrameInfo processRawFrame(const QByteArray& rawData,
                                   const QVariantMap& metadata) override {
        // Guide设备特定处理逻辑
    }

    QPixmap convertToDisplayFormat(const UnifiedFrameInfo& frameInfo) override {
        // Guide设备图像转换
    }
};
```

### 2. 注册适配器

```cpp
// 在AdapterFactory中注册
AdapterFactory::registerAdapter(DeviceType::GuideGigE, []() {
    return std::make_shared<GuideAdapter>();
});

// 在FrameProcessor中使用
auto guideAdapter = AdapterFactory::createAdapter(DeviceType::GuideGigE);
processor->registerAdapter(DeviceType::GuideGigE, guideAdapter);
```

## 测试和验证

### 运行测试程序

```bash
cd Z200/module/hikInfrared/test
qmake test.pro
make
./FrameProcessorTest
```

### 测试内容

1. 基本帧处理功能
2. 异步处理性能
3. 错误处理机制
4. 内存管理验证
5. 多设备支持测试

## 故障排除

### 常见问题

1. **适配器注册失败**
   - 检查设备类型是否正确
   - 确认适配器初始化成功

2. **图像显示异常**
   - 验证NV12数据格式
   - 检查图像尺寸参数

3. **性能问题**
   - 启用异步处理
   - 调整帧缓冲池大小

### 调试信息

启用详细日志输出：
```cpp
QLoggingCategory::setFilterRules("*.debug=true");
```

## 未来扩展

1. 支持更多红外设备类型
2. 添加GPU加速处理
3. 实现实时图像增强算法
4. 支持多路视频流处理
5. 集成机器学习算法

## 总结

新的统一帧处理架构提供了：

- ✅ 简化的数据结构设计
- ✅ 更好的设备扩展性
- ✅ 优化的性能表现
- ✅ 完整的向后兼容性
- ✅ 清晰的代码架构
- ✅ 全面的错误处理
- ✅ 详细的测试覆盖

这个架构解决了原有的视图层显示问题，为红外相机模块的未来发展奠定了坚实的基础。
