# PRPD图谱绘制和数据存储

## 1. 简介

PRPD (Phase Resolved Partial Discharge) 图谱是一种用于显示和分析局部放电信号的重要图表形式。本文档描述了系统中PRPD图谱的绘制流程和数据存储机制。

## 2. 数据结构

### 2.1 基本数据结构

PRPD图谱的基本数据结构定义在`Z200/view/prps/PrpdOriginal.h`中：

```cpp
typedef struct _PrpdData  // PRPD图谱保存的数据结构
{
    float fPeak;    // 幅度值
    float fPhase;   // 相位值
}PrpdData;
```

### 2.2 数据模型

系统使用`PhaseDataModel`管理相位数据，它存储了以下关键信息：

- 周期总数（`m_iPeriodCount`）
- 每周期相位个数（`m_iPhaseCount`）
- PRPD周期计数（`m_uiPRPDPeriodCnt`）

```cpp
PhaseDataModel *m_pModel;
PhaseModelDriver *m_pDriver;

qint32 m_iPeriodCount;          // 周期总数
qint32 m_iPhaseCount;           // 每周期相位个数
quint32 m_uiPRPDPeriodCnt;
```

## 3. 图谱绘制流程

### 3.1 数据采集与处理

1. 通过`onDataRead`函数接收原始数据（如HFCT::PRPSData）
2. 数据传递给图表组件的`setData`函数
3. 根据累积模式不同，进行相应处理：
   - 累积模式：只添加新增周期图谱的数据
   - 非累积模式：添加完整模型层当前数据

```cpp
void PrpsAbstractView::onAdvance(qint32 iStep)
{
    m_pPrps->setData(m_pDataModel->currentData());
    
    if(!model()->isPrpsAccumulateEnabled())
    {
        if(m_pPrpd->accumulationMode() == PrpdViewItem::ACCUMULATION)
        {
            QVector<quint16> vPeriods;
            for(int i = (m_iPeriodCount - 1); i > (m_iPeriodCount - iStep - 1); --i)
            {
                vPeriods << i;
            }
            m_pPrpd->setData(m_pDataModel->currentData(), vPeriods);
        }
        else
        {
            m_pPrpd->setData(m_pDataModel->currentData());
        }
    }
    else
    {
        m_pPrpd->setData(m_pDataModel->allData(), iStep);
    }
}
```

### 3.2 PRPD图谱绘制

图谱绘制通过`PrpdDraw`类实现，主要步骤包括：

1. 设置PRPD矩阵数据：`setPrpdMatrix`
2. 检查和重置图像对象：`checkAndResetImage`
3. 绘制坐标系：`drawCoordinate`
4. 绘制数据点：`drawDataItems`

绘制数据点的具体过程：

```cpp
void PrpdOriginal::drawPrpdModel()
{
    QPainter painter(&m_PRPDPixmap);

    QHashIterator<QString,QVector<PrpdData>> cololPoint(m_hRefreshData);
    while(cololPoint.hasNext())
    {
        cololPoint.next();
        painter.setPen(QPen(QColor(cololPoint.key()), POINT_SIZE, Qt::SolidLine));

        const QVector<PrpdData> &pointVector = cololPoint.value();
        for(INT32 i = 0; i < pointVector.count(); ++i)
        {
            if(pointVector.at(i).fPeak >= 0)
            {
                UINT16 posX = m_usOriginX + pointVector.at(i).fPhase * m_usPRPDWidth;
                UINT16 posY = m_usOriginY - (pointVector.at(i).fPeak * m_usPRPDHeight + ENTER_ONE_CONSTANT_VALUE) - POINT_SIZE;
                painter.drawPoint(posX, posY);
            }
        }
    }

    m_hRefreshData.clear();
    m_pPainter->drawPixmap(0, 0, m_PRPDPixmap);
}
```

## 4. 数据存储机制

### 4.1 数据存储结构

PRPD数据通过`PRPDDataMap`类管理，它继承自`PRPSDataMapBase`，主要包含：

- 图谱头信息（站点名称、设备名称、生成时间等）
- 图谱扩展信息（相位间隔数、量化幅度等）
- 图谱数据（幅度和相位值）
- 放电计数数据

### 4.2 数据保存过程

保存操作主要由`HFCTPrpsView::saveDataToFile`函数实现，流程如下：

1. 准备保存数据：通过`composeSavedData`组装数据
2. 设置图谱头信息：通过`setPRPDMapHead`设置
3. 填充PRPD数据：通过`setPRPDData`填充
4. 将数据保存到文件：

```cpp
bool PRPDDataMap::saveMapDataXML(XMLDocument *pDoc, QDomElement &element, bool bCrypt)
{
    pDoc->beginElement(element);
    pDoc->beginElement(XML_FILE_NODE_DATA);
    int iPerDataSize = perDataSize();
    
    // 保存主要数据
    QByteArray baDatas;
    baDatas = QByteArray::fromRawData((char*)m_pPrpdData, 
                                      m_stPRPDExt.iQuantificationAmp * 
                                      m_stPRPDExt.iPhaseIntervalCount * 
                                      iPerDataSize);
    QString strData = baDatas.toBase64();
    pDoc->setValue(XML_FILE_NODE_DATA, strData);

    // 保存颜色数据
    QByteArray baColor;
    baColor = QByteArray::fromRawData((char*)m_pPRPDColor, 
                                      3 * m_stPRPDExt.iQuantificationAmp * 
                                      m_stPRPDExt.iPhaseIntervalCount * 
                                      sizeof(quint8));
    QString strColor = baColor.toBase64();
    pDoc->setValue(XML_FILE_NODE_DATA_COLOR, strColor);

    // 保存放电计数数据
    QByteArray baDischargeCount;
    baDischargeCount = QByteArray::fromRawData((char*)m_pPRPDDischargeCnt, 
                                              m_stPRPDExt.iQuantificationAmp * 
                                              m_stPRPDExt.iPhaseIntervalCount * 
                                              sizeof(quint16));
    QString strDischargeCount = baDischargeCount.toBase64();
    pDoc->setValue(XML_FILE_NODE_DISCHARGE_COUNT, strDischargeCount);

    bool isSuccess = pDoc->save(bCrypt);
    return isSuccess;
}
```

### 4.3 二进制数据存储

对于需要更高效存储的场景，还支持二进制格式存储：

```cpp
void PRPDDataMap::saveMapDataBinary(QByteArray &baPackage)
{
    int iDataCnt = m_stPRPDExt.iPhaseIntervalCount * m_stPRPDExt.iQuantificationAmp;
    if(iDataCnt <= 0)
    {
        return;
    }
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::DoublePrecision);
    out.device()->seek(out.device()->size());

    double * daData = (double *)m_pPrpdData;
    for(int i = 0; i < iDataCnt; i++)
    {
        double dData = daData[i];
        out << dData;
    }
}
```

## 5. PRPS到PRPD的转换

PRPS (Phase Resolved Partial discharge Spectral)数据通过统计处理转换为PRPD图谱，在`setPRPDData`函数中实现：

1. 获取PRPS原始数据并进行动态范围转换
2. 根据相位偏移量调整相位
3. 计算放电次数统计数据
4. 生成PRPD图谱数据

```cpp
void HFCTPrpsView::setPRPDData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.vecPRRepeatyData.clear();
    int iDataCnt = stSavedData.stPRPDInfo.iPhaseIntervalCount * stSavedData.stPRPDInfo.iQuantificationAmp;

    UINT16 *pusPulsePRTotalCnt = new UINT16[iDataCnt];
    memset(pusPulsePRTotalCnt, 0, iDataCnt *sizeof(UINT16));
    UINT8 *pucDataColor = new UINT8[3*iDataCnt];
    UINT16 *pusDataDischargeCnt = new UINT16[iDataCnt];

    // 获取PRPS原始数据或转换动态范围
    QVector<qint16> vecPRPDData;
    if(m_eDynamicRange == SystemSet::DynamicRangeSwitch_On){
        vecPRPDData = convertPrpsToPrpd(m_pChart->prpsData(),
                                       stSavedData.stPRPDInfo.fAmpLowerLimit,
                                       stSavedData.stPRPDInfo.fAmpUpperLimit,
                                       stSavedData.stPRPDInfo.iPhaseIntervalCount);
    }else{
        vecPRPDData = m_pChart->prpdData();
    }

    int iAmpAreaCnt = vecPRPDData.size() / stSavedData.stPRPDInfo.iPhaseIntervalCount;
    int iPhaseShitStep = m_iPhaseAlias / (360 / stSavedData.stPRPDInfo.iPhaseIntervalCount);

    // 处理相位偏移并构建PRPD数据
    for(int j = 0; j < stSavedData.stPRPDInfo.iPhaseIntervalCount; j++)
    {
        for(int i = 0; i < iAmpAreaCnt; i++)
        {
            int iNewPhaseIndex = (j + iPhaseShitStep) % stSavedData.stPRPDInfo.iPhaseIntervalCount;

            qint16 sPRPD = vecPRPDData.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j);
            if(sPRPD < 0)
            {
                sPRPD = 0;
            }
            pusPulsePRTotalCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = sPRPD;

            // 获取并保存颜色数据
            QRgb color = m_pChart->prpdDataColor(i,j);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i] = qRed(color);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+1] = qGreen(color);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+2] = qBlue(color);
            
            // 获取放电计数数据
            pusDataDischargeCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = m_pChart->prpdDataPulseCnt(i,j);
        }
    }

    // 计算重复率
    double *pdPRRepeatyProb = new double[iDataCnt];
    memset(pdPRRepeatyProb, 0, iDataCnt *sizeof(double));

    for(int i = 0; i < iDataCnt; i++)
    {
        pdPRRepeatyProb[i] = (double)pusPulsePRTotalCnt[i] / (double)stSavedData.stPRPDInfo.iPowerFreCycleCount;
        pdPRRepeatyProb[i] = pdPRRepeatyProb[i] / (20.0 / 1000.0);
        stSavedData.vecPRRepeatyData.append(pdPRRepeatyProb[i]);

        // 保存颜色数据
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+1]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+2]);

        // 保存放电计数数据
        stSavedData.vecPRPDDataDischargeCnt.append(pusDataDischargeCnt[i]);
    }
    
    // 释放内存
    delete [] pdPRRepeatyProb;
    delete [] pusPulsePRTotalCnt;
    delete [] pucDataColor;
    delete [] pusDataDischargeCnt;
}
```

## 6. 应用场景

PRPD图谱在局部放电检测中具有重要意义，可用于：

1. 局部放电模式识别
2. 故障类型判断
3. 设备状态监测
4. 绝缘性能评估

通过分析PRPD图谱的特征（如集中区域、放电分布特点等），可以判断设备中可能存在的缺陷类型和严重程度。 

## 7. 类图关系

下面是PRPD图谱绘制和数据存储相关类的类关系图：

```mermaid
classDiagram
    class PrpdData {
        +float fPeak
        +float fPhase
    }
    
    class PhaseDataModel {
        +qint32 m_iPeriodCount
        +qint32 m_iPhaseCount
        +quint32 m_uiPRPDPeriodCnt
        +currentData()
        +allData()
    }
    
    class PhaseModelDriver {
        +drive()
    }
    
    class PrpsAbstractView {
        +onAdvance(qint32 iStep)
        +setData()
    }
    
    class QWidget {
        +paintEvent()
        +resizeEvent()
    }
    
    class PrpdOriginal {
        -UINT16 m_usPRPDWidth
        -UINT16 m_usPRPDHeight
        -AccumulateMode m_eAccumulateMode
        -QPainter *m_pPainter
        -QPixmap m_PRPDPixmap
        -QHash~QString,QVector~PrpdData~~ m_hRefreshData
        +setAccumulateMode(AccumulateMode)
        +setZAxisScale(PRPSDataType, PRPSDataType)
        +switchRefrence(bool)
        +setPowerFreq(Frequency)
        +paintEvent(QPaintEvent*)
        +resizeEvent(QResizeEvent*)
        -drawPrpdModel()
        -drawPrpdCoordinate()
        -resetParams()
        -dataInit()
    }
    
    class PrpsBase {
        -PRPSModel m_PRPSModel
        -PRPSVECTOR m_vRawData
        -Frequency m_ePowerFrequency
        +translatePhase(INT32)
        +setPhaseCount(int)
        +setPowerFreq(Frequency)
        +setZAxisScale(PRPSDataType, PRPSDataType)
        +setData(const PRPSVECTOR&)
        +currentDatas() const
        +clear()
        -drawPRPSModel()
        -drawPrpsCoordinate()
        -makePRPSModel()
    }
    
    class PRPDDataMap {
        -PRPDMapHead m_stPRPDMapHead
        -PRPDMapExt m_stPRPDExt
        -void* m_pPrpdData
        -quint8* m_pPRPDColor
        -quint16* m_pPRPDDischargeCnt
        +setPRPDMapHead(PRPDMapHead&)
        +setPRPDData(void*, int)
        +setPRPDDataColor(quint8*, int)
        +setPRPDDataDischargeCnt(quint16*, int)
        +saveMapDataXML(XMLDocument*, QDomElement&, bool)
        +saveMapDataBinary(QByteArray&)
    }
    
    class HFCTPrpsView {
        -PhaseDataModel* m_pDataModel
        -PhaseModelDriver* m_pDriver
        -PrpdViewItem* m_pPrpd
        -SystemSet::DynamicRange m_eDynamicRange
        -int m_iPhaseAlias
        +setPRPDData(HFCTPRPSPRPDDataInfo&)
        +saveDataToFile()
        +composeSavedData()
        -convertPrpsToPrpd(QVector~qint16~&, float, float, int)
    }
    
    QWidget <|-- PrpdOriginal
    QWidget <|-- PrpsBase
    PrpdOriginal --o PrpsAbstractView : uses
    PrpsBase --o PrpsAbstractView : uses
    PhaseDataModel --o PrpsAbstractView : m_pDataModel
    PhaseModelDriver --o PrpsAbstractView : m_pDriver
    PrpdViewItem --o HFCTPrpsView : m_pPrpd
    PhaseDataModel --o HFCTPrpsView : m_pDataModel
    PhaseModelDriver --o HFCTPrpsView : m_pDriver
    PrpdData --* PRPDDataMap : contains data structure
    PrpdData --o PrpdOriginal : uses
    PRPDDataMap --o HFCTPrpsView : uses for data storage
```

上图展示了PRPD图谱绘制和数据存储相关的主要类及其关系：

1. **基础数据结构**：
   - `PrpdData`：保存幅度值和相位值的基本数据结构

2. **数据模型层**：
   - `PhaseDataModel`：管理相位数据，存储周期总数、每周期相位个数等信息
   - `PhaseModelDriver`：数据模型驱动器

3. **视图层**：
   - `PrpsAbstractView`：图表视图抽象类，处理数据更新和显示
   - `PrpdOriginal`：PRPD图谱的基类，继承自QWidget，负责基本绘制功能
   - `PrpsBase`：PRPS图谱的基类，提供坐标系绘制等基础功能

4. **数据存储层**：
   - `PRPDDataMap`：PRPD数据存储类，负责数据的存储、编码和保存
   - `HFCTPrpsView`：HFCT PRPS视图类，集成了数据处理和保存功能

这些类相互协作，完成PRPD图谱的数据处理、绘制和存储功能。类之间的关系包括继承关系和组合关系，形成了清晰的层次结构。 

## 8. 数据流程详解

本章节详细描述PRPD图谱系统中数据的完整流转过程，从用户输入数据到获取处理后的数据，理解整个流程有助于开发和维护系统。

### 8.1 数据流程时序图

以下时序图展示了从用户调用`m_pChart->setData(vData)`输入数据到`m_pChart->prpdData()`获取数据的完整过程：

```mermaid
sequenceDiagram
    participant 用户代码
    participant HFCTPrpsView
    participant PrpsBase
    participant PrpdOriginal
    participant PrpdPointMatrix
    participant PhasePaintData
    participant PrpdDraw
    
    用户代码->>HFCTPrpsView: m_pChart->setData(vData)
    HFCTPrpsView->>PrpsBase: setData(vData)
    PrpsBase->>PrpsBase: 保存原始数据到m_vRawData
    PrpsBase->>PrpsBase: 处理相位数据
    PrpsBase->>PrpsBase: makePRPSModel()生成模型
    PrpsBase->>PrpsBase: 更新m_PRPSModel
    PrpsBase->>PrpsBase: 更新显示(repaint)
    HFCTPrpsView->>PrpdOriginal: setData(数据)
    PrpdOriginal->>PrpdOriginal: 处理数据并进行相位调整
    
    note over PrpdOriginal: PRPD图表数据处理阶段
    PrpdOriginal->>PrpdPointMatrix: updatePaintContents(iStartPeriodIndex)
    PrpdPointMatrix->>PhasePaintData: 获取periodCount、phaseCount和percent数据
    PrpdPointMatrix->>PrpdPointMatrix: 设置当前周期数(setPeriodCount)
    PrpdPointMatrix->>PrpdPointMatrix: 根据数据百分比计算值在矩阵中的位置
    PrpdPointMatrix->>PrpdPointMatrix: 累积每个位置的出现次数(value++)
    PrpdPointMatrix->>PrpdPointMatrix: 如果超出累积时长，移除最早的数据
    PrpdOriginal->>PrpdOriginal: 保存到内部数据结构
    PrpdOriginal->>PrpdOriginal: addSample(处理后的数据)
    PrpdOriginal->>PrpdOriginal: 保存到m_hRefreshData
    
    note over PrpdOriginal: PRPD图表绘制阶段
    PrpdOriginal->>PrpdDraw: drawPrpdModel()
    PrpdDraw->>PrpdDraw: 设置绘图参数(颜色、点大小等)
    PrpdDraw->>PrpdPointMatrix: 获取每个位置的值和重复率
    PrpdDraw->>PrpdDraw: 绘制PRPD点阵数据到图像缓存
    
    note over HFCTPrpsView: 用户获取PRPD数据流程
    用户代码->>HFCTPrpsView: 请求PRPD数据
    HFCTPrpsView->>PrpdOriginal: prpdData()
    PrpdOriginal->>PrpdPointMatrix: 获取点阵数据(m_qvtPRPDData)
    PrpdPointMatrix->>PrpdPointMatrix: 根据累积时长生成输出数据
    PrpdPointMatrix-->>PrpdOriginal: 返回处理后的PRPD数据
    PrpdOriginal-->>HFCTPrpsView: 返回处理后的PRPD数据
    HFCTPrpsView-->>用户代码: 返回处理后的PRPD数据
```

### 8.2 详细流程解析

#### 8.2.1 数据输入阶段

当用户调用`m_pChart->setData(vData)`将原始数据传入系统时：

```cpp
// 在HFCTPrpsView类中的调用示例
m_pChart->setData(vData); // 将数据刷新到图谱上
```

- 这里的`m_pChart`通常是`PrpsBase`类型的对象或其子类对象
- `vData`是原始的相位分辨数据，通常是PRPS (Phase Resolved Partial discharge Spectral)格式

#### 8.2.2 PRPS数据处理

`PrpsBase::setData()`方法接收原始数据并进行处理：

```cpp
void PrpsBase::setData(const PRPSVECTOR &rawData)
{
    // 1. 保存原始数据
    m_vRawData = rawData;
    
    // 2. 处理数据并生成模型
    makePRPSModel();
    
    // 3. 触发重绘
    update();
}
```

在这个过程中：
- 原始数据被保存到内部变量`m_vRawData`
- 调用`makePRPSModel()`将原始数据转换为PRPS模型格式
- 该模型存储在内部`m_PRPSModel`变量中，这是一个包含线段信息的哈希表结构
- 触发GUI更新，显示新的PRPS图谱

#### 8.2.3 PRPD数据转换

PRPS数据需要经过处理才能转换为PRPD图谱，这个转换过程主要依赖于`PrpdPointMatrix`类的实现：

1. **数据接收**：原始PRPS数据通过`setData`方法传入系统
2. **数据处理**：`updatePaintContents`方法处理数据并更新到点阵矩阵
   - 系统将PRPS值转换为相对百分比(percent)
   - 根据百分比计算数据在矩阵中的行索引
   - 对应位置计数器递增(value++)，表示该位置出现次数增加
3. **累积控制**：`setAccumulativeTime`设置累积时长（例如1s, 5s, 15s, 30s, 60s）
   - 系统根据每秒数据量和累积时长计算最大累积数据量
   - 超出累积时长的旧数据会被自动移除(takeFirst)，对应位置计数减少
4. **数据查询**：`value`和`repetitionRate`方法提供数据访问接口
   - `value`返回原始计数值
   - `repetitionRate`计算重复率(计数值/总周期数)

PrpdPointMatrix类的核心代码实现了这些功能：

```cpp
// 设置累积时长（秒）
void PrpdPointMatrix::setAccumulativeTime(int iSecond)
{
    m_uiAccumulativeTime = iSecond;
    updateAccumulativeDataCount();
}

// 更新累积数据总数
void PrpdPointMatrix::updateAccumulativeDataCount()
{
    m_uiAccumulativeDataCount = m_uiAccumulativeTime * m_uiDataCountPerSecond;
    m_accumulativeData.clear();
    m_accumulativeData.reserve(m_uiAccumulativeDataCount);
    reset();
}

// 移除超出累积时长的旧数据
void PrpdPointMatrix::advance(int iStep)
{
    if (iStep <= 0) return;
    
    if (m_accumulativeData.size() + iStep > m_uiAccumulativeDataCount)
    {
        int iOverflowCount = m_accumulativeData.size() + iStep - m_uiAccumulativeDataCount;
        for (int i = 0; i < iOverflowCount; ++i)
        {
            QVector<QVector<int> > qvtTakeData = m_accumulativeData.takeFirst();
            // 从当前累积数据中减去移除的旧数据
            for (int j = 0; j < m_iRowCount; ++j)
            {
                for (int k = 0; k < m_iColumnCount; ++k)
                {
                    m_qvtPRPDData[j][k] -= qvtTakeData[j][k];
                }
            }
        }
    }
}
```

当用户调用`prpdData()`获取数据时，系统会将这个矩阵数据转换为一维向量返回，确保数据与当前累积设置一致。

#### 8.2.4 PRPD数据处理与显示

处理后的数据通过`PrpdOriginal::addSample()`方法进行处理和显示：

```cpp
void PrpdOriginal::addSample(const QHash<QString,QVector<PrpdData>> &hRawData)
{
    // 1. 添加数据到刷新缓存
    m_hRefreshData = hRawData;
    
    // 2. 绘制PRPD模型
    drawPrpdModel();
    
    // 3. 更新显示
    update();
}
```

在`drawPrpdModel()`方法中：
- 数据点根据其幅值和相位值转换为屏幕坐标
- 使用对应的颜色绘制到`m_PRPDPixmap`图像缓存中
- 最终通过`m_pPainter->drawPixmap()`方法显示到界面上

```cpp
void PrpdOriginal::drawPrpdModel()
{
    QPainter painter(&m_PRPDPixmap);

    QHashIterator<QString,QVector<PrpdData>> cololPoint(m_hRefreshData);
    while(cololPoint.hasNext())
    {
        cololPoint.next();
        painter.setPen(QPen(QColor(cololPoint.key()), POINT_SIZE, Qt::SolidLine));

        const QVector<PrpdData> &pointVector = cololPoint.value();
        for(INT32 i = 0; i < pointVector.count(); ++i)
        {
            if(pointVector.at(i).fPeak >= 0)
            {
                UINT16 posX = m_usOriginX + pointVector.at(i).fPhase * m_usPRPDWidth;
                UINT16 posY = m_usOriginY - (pointVector.at(i).fPeak * m_usPRPDHeight + ENTER_ONE_CONSTANT_VALUE) - POINT_SIZE;
                painter.drawPoint(posX, posY);
            }
        }
    }

    m_hRefreshData.clear();
    m_pPainter->drawPixmap(0, 0, m_PRPDPixmap);
}
```

#### 8.2.5 数据获取过程

当用户需要获取处理后的PRPD数据时，调用`m_pChart->prpdData()`：

```cpp
// 用户代码示例
QVector<qint16> vecPRPDData = m_pChart->prpdData();
```

这个过程会返回内部处理后的PRPD数据，用户获得的是已经经过处理、转换并可能累积后的数据，而非原始输入数据。

### 8.3 数据流程中的关键环节

1. **数据模型与视图分离**
   - `PhaseDataModel`维护数据模型，存储原始相位数据
   - `PrpsBase`和`PrpdOriginal`等类负责视图显示
   - 这种分离使系统更加灵活和可维护

2. **累积模式的处理**
   - 系统支持累积和非累积两种模式
   - 累积模式下，新数据会被添加到现有数据中，形成累积效果
   - 非累积模式下，每次只显示最新的数据

3. **相位偏移处理**
   - 系统支持相位偏移调整，用户可以调整PRPD图谱的相位显示
   - 偏移通过`translatePhase()`方法实现，影响数据在图谱上的位置

4. **动态范围转换**
   - 当启用动态范围功能时，系统会对PRPS数据进行特殊处理，再转换为PRPD数据
   - 这通过`convertPrpsToPrpd()`方法实现，确保数据在指定的幅值范围内

5. **数据存储与导出**
   - 处理后的数据可以通过`saveDataToFile()`方法保存到文件
   - 支持XML和二进制两种格式
   - 存储内容包括图谱数据、颜色信息和放电计数数据

通过理解这个完整的数据流程，开发人员可以更好地维护和扩展PRPD图谱系统，确保数据处理的准确性和效率。 

## 9. PRPD数据量程问题与解决方案

### 9.1 问题描述

在局部放电检测系统中，PRPD图谱的数据会受到图谱幅值量程设置的影响。当启用动态量程时，`m_pChart->prpdData()`返回的数据是基于动态调整后的量程计算的，这导致以下几个关键问题：

1. **数据一致性问题**：在不同量程条件下进行数据比较时，得到的结果可能存在显著差异，影响诊断的准确性。
2. **固定量程需求**：用户在数据保存、分析和录屏等场景下，通常希望获取基于固定量程生成的PRPD数据。
3. **累积时长影响**：系统中累积不是简单的布尔状态，而是通过`m_iAccumulationTime`（整数值）来控制累积时长（1、5、15、30或60），这会直接影响PRPD数据的累积范围和统计结果。

特别注意的是，PRPS数据是原始的相位分辨数据，不存在累积概念；而PRPD数据是统计分析结果，支持数据累积。这一特性使得简单地缓存原始PRPS数据并不能完全解决问题，尤其是在需要处理不同累积时长场景下的数据需求时。

### 9.2 完善的解决方案

#### 9.2.1 固定量程周期数据缓存策略

我们设计了一个高效的缓存策略，基于每个周期的固定量程PRPD数据，以便支持不同累积时长下的数据需求：

```cpp
// 缓存每个周期的固定量程PRPD数据
QMap<int, QVector<qint16>> m_mapPeriodFixedRangePrpdData;
```

这种数据结构具有以下优势：

1. **高效累积计算**：可以根据累积时长动态计算不同时间跨度的累积结果
2. **内存高效**：只保存必要的固定量程处理后的数据
3. **兼容不同累积设置**：无需为每种累积设置单独维护缓存

具体实现方法是使用周期索引作为键，对应的固定量程PRPD数据作为值，这样可以在需要时迅速获取任意时长的累积数据。

#### 9.2.2 数据缓存管理机制

针对缓存数据的管理，我们设计了以下机制：

##### 1. 数据更新时机

为确保数据的准确性和一致性，系统在以下关键时刻更新固定量程PRPD数据缓存：

- **新数据接收时**：在`onDataRead()`接收新数据时，同步处理并存储每个周期的固定量程数据
- **量程参数变化时**：当用户调整幅值上下限时，标记缓存需要全部刷新
- **累积时长变化时**：在`setAccumulationTime()`方法中检测并调整缓存大小

##### 2. 增量更新策略

为了提高系统性能，我们采用增量更新策略：

```cpp
// 当前周期索引
int currentPeriodIndex = m_iPeriodCount - 1;

// 只处理最新的周期数据，而不是每次都重新处理所有历史数据
QVector<qint16> fixedRangePrpdData = convertPrpsToPrpd(
    currentPrpsData,
    m_nAmpLowerLimit,
    m_nAmpUpperLimit,
    m_stPRPDInfo.iPhaseIntervalCount
);

// 保存到缓存
m_mapPeriodFixedRangePrpdData[currentPeriodIndex] = fixedRangePrpdData;
```

这种方式显著减少了CPU负载，特别是在长时间运行系统时。

##### 3. 缓存内存管理

为防止长时间运行导致内存占用过大，设计了自动缓存大小限制机制：

```cpp
// 限制缓存大小，防止内存无限增长
void limitCacheSize() {
    // 获取当前系统工频(50Hz或60Hz)
    int powerFrequency = (m_ePowerFrequency == FREQ_50) ? 50 : 60;
    
    // 计算需要保留的周期数
    int requiredPeriods = 0;
    
    // 判断是否处于录屏模式
    if (m_bIsRecording) {
        // 录屏模式下，需要保留足够的数据支持录制
        // 使用录制时保存的累积时长
        requiredPeriods = m_iRecordingAccumulationTime * powerFrequency;
        
        // 考虑可能的最大录制时长(60秒)作为下限，确保长时间录制有足够数据
        int maxRecordingPeriods = 60 * powerFrequency; // 60秒是通常支持的最大录制时长
        requiredPeriods = qMax(requiredPeriods, maxRecordingPeriods);
    } else {
        // 非录屏模式，使用当前累积时长
        requiredPeriods = m_iAccumulationTime * powerFrequency;
    }
    
    // 添加冗余系数，提高系统稳定性
    // 冗余系数可根据实际系统需求调整，这里使用2作为示例
    int maxRequiredPeriods = requiredPeriods * 2;
    
    // 确保至少保留一定数量的周期，避免累积时长为0时缓存过小
    maxRequiredPeriods = std::max(maxRequiredPeriods, 100);
    
    // 录屏模式下额外检查，确保有足够的历史数据
    if (m_bIsRecording) {
        // 录屏通常需要更多历史数据，确保缓存足够大
        maxRequiredPeriods = std::max(maxRequiredPeriods, 300); // 确保至少5秒数据(60Hz)
    }
    
    // 如果缓存太大，移除最旧的数据
    if (m_mapPeriodFixedRangePrpdData.size() > maxRequiredPeriods) {
        // 排序所有周期索引
        QList<int> periodIndices = m_mapPeriodFixedRangePrpdData.keys();
        std::sort(periodIndices.begin(), periodIndices.end());
        
        // 计算并移除过旧的数据
        int removeCount = m_mapPeriodFixedRangePrpdData.size() - maxRequiredPeriods;
        for (int i = 0; i < removeCount; ++i) {
            m_mapPeriodFixedRangePrpdData.remove(periodIndices[i]);
        }
    }
}
```

#### 9.2.3 固定量程数据的计算与累积

系统需要提供一个灵活的接口，能够根据当前累积时长设置动态计算累积结果：

```cpp
// 获取指定累积时长的固定量程PRPD数据
QVector<qint16> HFCTPrpsView::getFixedRangePrpdData() {
    // 检查数据模型是否存在
    if (!m_pDataModel) {
        return QVector<qint16>();
    }
    
    // 如果需要更新缓存或缓存为空
    if (m_bNeedUpdateFixedRangeData || m_mapPeriodFixedRangePrpdData.isEmpty()) {
        updateAllPeriodData();
        m_bNeedUpdateFixedRangeData = false;
    }
    
    // 获取当前工频
    int powerFrequency = (m_ePowerFrequency == FREQ_50) ? 50 : 60;
    
    // 计算需要的周期数量
    int requiredPeriods = m_iAccumulationTime * powerFrequency;
    if (requiredPeriods <= 0) {
        requiredPeriods = 1; // 确保至少处理一个周期
    }
    
    // 计算起始周期索引
    int startPeriod = m_iPeriodCount - requiredPeriods;
    startPeriod = qMax(0, startPeriod); // 确保不小于0
    
    // 获取相位和幅值数量
    int phaseCount = m_stPRPDInfo.iPhaseIntervalCount;
    int ampCount = PRPSMapNS::QUANTIFICATION_AMP;
    int matrixSize = phaseCount * ampCount;
    
    // 创建并初始化累积结果数组
    QVector<qint16> accumulatedData(matrixSize, 0);
    
    // 累积指定时长内的数据
    int processedPeriods = 0;
    for (int i = startPeriod; i < m_iPeriodCount; ++i) {
        if (m_mapPeriodFixedRangePrpdData.contains(i)) {
            const QVector<qint16>& periodData = m_mapPeriodFixedRangePrpdData[i];
            if (periodData.size() == matrixSize) {
                for (int j = 0; j < matrixSize; ++j) {
                    accumulatedData[j] += periodData[j];
                }
                processedPeriods++;
            }
        }
    }
    
    // 如果没有处理任何数据，尝试使用最新的单周期数据
    if (processedPeriods == 0 && !m_mapPeriodFixedRangePrpdData.isEmpty()) {
        // Qt 4.8.7不支持lastKey()方法，使用keys().last()替代
        int latestPeriod = m_mapPeriodFixedRangePrpdData.keys().last();
        const QVector<qint16>& latestData = m_mapPeriodFixedRangePrpdData[latestPeriod];
        if (latestData.size() == matrixSize) {
            return latestData;
        }
    }
    
    return accumulatedData;
}

// 更新所有周期数据的方法实现
void HFCTPrpsView::updateAllPeriodData() {
    // 清空现有缓存
    m_mapPeriodFixedRangePrpdData.clear();
    
    // 获取所有可用周期数
    int availablePeriods = qMin(m_iPeriodCount, 1000); // 限制最大处理周期数以避免性能问题
    
    // 逐个处理每个周期
    for (int i = m_iPeriodCount - availablePeriods; i < m_iPeriodCount; ++i) {
        if (i < 0) continue; // 跳过无效索引
        
        // 获取单个周期的PRPS数据
        QVector<qint16> periodPrpsData = m_pDataModel->getPeriodData(i);
        if (periodPrpsData.isEmpty()) continue;
        
        // 转换为固定量程PRPD数据
        QVector<qint16> periodPrpdData = convertPrpsToPrpd(
            periodPrpsData,
            m_stPRPDInfo.fAmpLowerLimit,
            m_stPRPDInfo.fAmpUpperLimit,
            m_stPRPDInfo.iPhaseIntervalCount
        );
        
        // 保存到缓存
        m_mapPeriodFixedRangePrpdData[i] = periodPrpdData;
    }
    
    // 限制缓存大小
    limitCacheSize();
}

// 增量更新单个周期数据
void HFCTPrpsView::updateSinglePeriodData(int periodIndex) {
    if (periodIndex < 0 || !m_pDataModel) return;
    
    // 获取指定周期的PRPS数据
    QVector<qint16> periodPrpsData = m_pDataModel->getPeriodData(periodIndex);
    if (periodPrpsData.isEmpty()) return;
    
    // 转换为固定量程PRPD数据
    QVector<qint16> periodPrpdData = convertPrpsToPrpd(
        periodPrpsData,
        m_stPRPDInfo.fAmpLowerLimit,
        m_stPRPDInfo.fAmpUpperLimit,
        m_stPRPDInfo.iPhaseIntervalCount
    );
    
    // 保存到缓存
    m_mapPeriodFixedRangePrpdData[periodIndex] = periodPrpdData;
    
    // 可选：如果新周期导致缓存过大，可以在此处调用limitCacheSize()
    if (m_mapPeriodFixedRangePrpdData.size() % 100 == 0) {
        limitCacheSize();
    }
}
```

上述实现包含以下关键优化和安全措施：

1. **数据模型检查**：函数开始时检查数据模型是否存在，避免空指针访问。
2. **懒加载机制**：只在真正需要时才进行全量数据更新，避免不必要的计算。
3. **动态累积计算**：根据当前工频和累积时长精确计算所需周期数。
4. **边界条件处理**：处理了各种可能的边界情况，如累积时长为0、缓存为空等。
5. **增量更新支持**：通过`updateSinglePeriodData()`提供单周期更新功能，减少系统负载。
6. **容错机制**：当没有足够有效数据时提供回退策略，确保始终返回有效数据。
7. **数据大小验证**：在累积操作前验证数据大小，避免数组越界访问。

这种设计使`getFixedRangePrpdData()`函数能够安全高效地处理各种实际场景，为用户提供一致且准确的PRPD数据。

#### 9.2.4 特殊场景处理

系统需要在多种特殊场景下确保数据一致性：

##### 1. 数据保存场景

在保存PRPD数据时，需要确保使用固定量程生成的数据：

```cpp
void setPRPDData(HFCTPRPSPRPDDataInfo &stSavedData) {
    // 设置累积标志为累积时长
    stSavedData.stPRPDInfo.isAccumulated = m_iAccumulationTime;
    
    // 获取PRPD数据
    QVector<qint16> vecPRPDData;
    if (m_eDynamicRange == SystemSet::DynamicRangeSwitch_On) {
        // 根据累积状态选择数据
        if (m_iAccumulationTime > 0) {
            vecPRPDData = getFixedRangePrpdData();
        } else {
            vecPRPDData = convertPrpsToPrpd(
                m_pChart->prpsData(),
                stSavedData.stPRPDInfo.fAmpLowerLimit,
                stSavedData.stPRPDInfo.fAmpUpperLimit,
                stSavedData.stPRPDInfo.iPhaseIntervalCount
            );
        }
    } else {
        vecPRPDData = m_pChart->prpdData();
    }
    
    // 处理相位偏移和后续处理...
}
```

##### 2. 录屏和回放场景

录屏开始时需要保存当前的量程参数和累积时长，以确保回放时可以获得一致的数据：

```cpp
void startRecording() {
    // 确保数据缓存是最新的
    updateAllPeriodData();
    
    // 保存当前的量程参数，确保录制期间一致性
    m_fRecordingAmpLowerLimit = m_stPRPDInfo.fAmpLowerLimit;
    m_fRecordingAmpUpperLimit = m_stPRPDInfo.fAmpUpperLimit;
    m_iRecordingAccumulationTime = m_iAccumulationTime;
    
    // 设置录制标志
    m_bIsRecording = true;
}
```

录屏期间使用保存的参数进行数据处理：

```cpp
QVector<qint16> getRecordingPrpdData() {
    // 使用录制时保存的参数处理数据
    PhaseData recordData = m_pDataModel->recordData();
    
    // 计算要累积的周期数
    int accumulateCount = m_iRecordingAccumulationTime;
    
    // 根据录制时的参数处理PRPS数据并累积
    // ...
    
    return result;
}
```

### 9.3 数据转换过程优化

为了保证高效处理，PRPS到PRPD的转换过程需要优化：

```cpp
QVector<qint16> convertPrpsToPrpd(const QVector<qint16>& prpsData,
                                 float lowerLimit,
                                 float upperLimit,
                                 int phaseCount) {
    // 创建结果数组
    QVector<qint16> prpdData(PRPSMapNS::QUANTIFICATION_AMP * phaseCount, 0);
    
    // 计算量程区间大小
    float rangeStep = (upperLimit - lowerLimit) / PRPSMapNS::QUANTIFICATION_AMP;
    
    // 高效处理数据
    int periodCount = prpsData.size() / phaseCount;
    for (int period = 0; period < periodCount; ++period) {
        for (int phase = 0; phase < phaseCount; ++phase) {
            float value = prpsData[period * phaseCount + phase];
            
            // 跳过无效值
            if (value < lowerLimit) continue;
            
            // 计算该值在量程中的位置
            int ampIndex = qMin(static_cast<int>((value - lowerLimit) / rangeStep), 
                               PRPSMapNS::QUANTIFICATION_AMP - 1);
            
            // 累积到结果中
            prpdData[ampIndex * phaseCount + phase]++;
        }
    }
    
    return prpdData;
}
```

### 9.4 接口设计与透明调用

为了确保现有代码与新机制的平滑集成，我们重写`prpdData()`方法，使其根据当前系统状态自动返回正确的数据：

```cpp
QVector<qint16> prpdData() {
    // 如果是录制状态，使用录制专用数据处理
    if (m_bIsRecording) {
        return getRecordingPrpdData();
    }
    
    // 根据动态量程和累积设置选择合适的数据处理方式
    if (m_eDynamicRange == SystemSet::DynamicRangeSwitch_On) {
        if (m_iAccumulationTime > 0) {
            return getFixedRangePrpdData();
        } else {
            return convertPrpsToPrpd(
                m_pChart->prpsData(),
                m_nAmpLowerLimit,
                m_nAmpUpperLimit,
                m_stPRPDInfo.iPhaseIntervalCount
            );
        }
    } else {
        return m_pChart->prpdData();
    }
}
```

为接口设计与透明调用的关键部分补充`getRecordingPrpdData`函数的完整实现：

```cpp
/**
 * @brief 获取录制状态下的PRPD数据，使用录制开始时保存的量程参数
 * 
 * 该函数确保在录制过程中，即使用户调整了量程设置，
 * 返回的PRPD数据仍然基于录制开始时的参数，保持数据一致性
 */
QVector<qint16> HFCTPrpsView::getRecordingPrpdData() {
    // 检查缓存是否为空
    if (m_mapPeriodFixedRangePrpdData.isEmpty()) {
        return QVector<qint16>();
    }
    
    // 获取相位和幅值数量
    int phaseCount = m_stPRPDInfo.iPhaseIntervalCount;
    int ampCount = PRPSMapNS::QUANTIFICATION_AMP;
    int matrixSize = phaseCount * ampCount;
    
    // 创建结果数组
    QVector<qint16> result(matrixSize, 0);
    
    // 如果没有设置累积或累积时长为0，使用最新的一个周期数据
    if (m_iRecordingAccumulationTime <= 0) {
        // 获取最新的周期索引
        int latestPeriod = m_mapPeriodFixedRangePrpdData.keys().last();
        const QVector<qint16>& latestData = m_mapPeriodFixedRangePrpdData[latestPeriod];
        if (latestData.size() == matrixSize) {
            return latestData;
        }
        return result; // 如果大小不匹配，返回空结果
    }
    
    // 累积模式处理
    // 获取当前工频
    int powerFrequency = (m_ePowerFrequency == FREQ_50) ? 50 : 60;
    
    // 计算累积周期数量
    int periodCount = m_iRecordingAccumulationTime * powerFrequency;
    
    // 从缓存中获取所有周期索引并排序
    QList<int> periodIndices = m_mapPeriodFixedRangePrpdData.keys();
    std::sort(periodIndices.begin(), periodIndices.end());
    
    // 计算累积的起始周期索引
    int startIndex = qMax(0, periodIndices.size() - periodCount);
    
    // 清零结果数组
    for (int i = 0; i < matrixSize; ++i) {
        result[i] = 0;
    }
    
    // 累积指定数量的周期数据
    int processedPeriods = 0;
    for (int i = startIndex; i < periodIndices.size(); ++i) {
        int periodIndex = periodIndices[i];
        const QVector<qint16>& periodData = m_mapPeriodFixedRangePrpdData[periodIndex];
        
        // 检查数据大小是否匹配
        if (periodData.size() != matrixSize) {
            continue; // 跳过大小不匹配的数据
        }
        
        // 累积数据
        for (int j = 0; j < matrixSize; ++j) {
            result[j] += periodData[j];
        }
        processedPeriods++;
    }
    
    // 如果没有处理任何有效周期，尝试使用最新的单周期数据
    if (processedPeriods == 0 && !periodIndices.isEmpty()) {
        int latestPeriod = periodIndices.last();
        const QVector<qint16>& latestData = m_mapPeriodFixedRangePrpdData[latestPeriod];
        if (latestData.size() == matrixSize) {
            return latestData;
        }
    }
    
    return result;
}
```

录制场景的特殊之处在于需要确保整个录制过程中使用一致的参数，即使用户在录制过程中调整了设置。上述实现通过以下机制确保数据一致性：

1. **缓存数据使用**：直接从预先处理好的周期PRPD数据缓存中获取数据
2. **累积计算**：根据录制时的累积设置精确计算需要处理的周期范围
3. **数据验证**：检查每个周期数据的有效性，跳过无效数据
4. **应急处理**：当没有有效数据时，提供合理的回退方案

这种设计确保了在录制和回放过程中看到的PRPD图谱数据始终保持一致，无论用户在录制过程中如何调整系统参数。

### 9.5 实施策略和注意事项

#### 9.5.1 性能优化策略

1. **懒加载机制**：只在真正需要时才更新全部缓存数据
2. **增量更新**：每次只处理新增周期数据，避免频繁重复计算
3. **内存限制**：动态调整缓存大小，防止长时间运行导致内存耗尽
4. **批量处理**：累积计算时尽可能使用批量操作提高效率

#### 9.5.2 内存管理

缓存大小应当与实际需要的累积时长和工频相关联，可以设置为：

```cpp
// 获取当前系统工频(50Hz或60Hz)
int powerFrequency = (m_ePowerFrequency == FREQ_50) ? 50 : 60;

// 计算精确需要的周期数
int exactRequiredPeriods = m_iAccumulationTime * powerFrequency;

// 根据实际需求决定是否添加冗余系数
int maxRequiredPeriods = exactRequiredPeriods; // 无冗余版本
// 或
int maxRequiredPeriods = exactRequiredPeriods * 2; // 带冗余版本
maxRequiredPeriods = std::max(maxRequiredPeriods, 100);
```

这样可以保证：
- 至少保留100个周期的数据，确保基本功能正常
- 当累积时长不为零时，根据当前工频精确计算所需周期数
  - 例如：5秒累积时长，50Hz工频下精确需要 5*50=250 个周期
  - 例如：5秒累积时长，60Hz工频下精确需要 5*60=300 个周期

关于冗余系数的说明：
- 使用冗余系数的主要原因是提高系统稳定性，避免边界条件下的数据丢失
- 在数据接收不均匀或系统负载波动的情况下，冗余空间可以减少频繁的缓存管理操作
- 如果系统内存受限或数据到达非常稳定，可以考虑减小或去除这个冗余系数

#### 9.5.3 线程安全考虑

如果系统在多线程环境下运行，需要注意数据结构的并发访问问题：

```cpp
class HFCTPrpsView {
private:
    QMutex m_cacheMutex; // 保护缓存数据结构
    
public:
    QVector<qint16> getFixedRangePrpdData() {
        QMutexLocker locker(&m_cacheMutex);
        // 访问和计算数据...
    }
};
```

#### 9.5.4 边界条件处理

系统需要妥善处理各种边界条件：

1. **首次运行**：缓存为空时需要正确初始化
2. **参数变化**：量程参数变化时需要标记缓存失效
3. **累积时长变化**：累积时长变化时需要重新计算结果
4. **特殊数值**：处理原始数据中可能出现的特殊值（如负值或超出量程的值）

### 9.6 方案总结

本方案通过缓存每个周期的固定量程PRPD数据，结合高效的数据管理策略，解决了PRPD图谱在动态量程下的数据一致性问题。主要优势包括：

1. **数据一致性**：确保在不同条件下生成的PRPD数据具有可比性
2. **灵活适应累积设置**：支持不同累积时长下的数据需求
3. **高效内存使用**：通过增量更新和自动缓存限制优化内存使用
4. **透明接口**：现有代码无需大量修改即可集成新机制
5. **特殊场景支持**：妥善处理数据保存、录制和回放等特殊场景

通过实施本方案，PRPD图谱系统将能够在保持高性能的同时，为用户提供更加准确、一致的数据分析结果。 