---
description: 
globs: 
alwaysApply: false
---
# 日报格式规范

日报系统使用特定的格式和结构来记录工作内容。本规则文件描述了正确的日报格式标准。

## 日报文件位置
- 日报文件保存在 [archived_reports/daily_report_2025_04.md](mdc:archived_reports/daily_report_2025_04.md) 等文件中
- 文件按照月份组织，命名格式为 `daily_report_YYYY_MM.md`

## 日报条目格式

每个工作日的日报应包含以下部分：

1. **日期标题**：
   ```
   **日期:** 2025年4月24日
   ```

2. **项目名称**：
   ```
   ## 项目名称：T95手持终端固件开发与测试项目
   ```

3. **工作内容列表**：
   ```
   ### 今日工作内容
   
   1. **任务名称** [工作标签]
      - **时间:** [小时数]
      - **分析过程:** 详细描述问题分析和发现过程
      - **解决方案:** 详细描述解决方法和实际效果
   ```

4. **今日工作总结**：
   ```
   ### 今日工作总结
   
   - **总计工时:** 总小时数
   - **工作类型分布:**
     - 类型1: 小时数
     - 类型2: 小时数
   - **核心成果:**
     - 成果1
     - 成果2
   ```

## 工作标签规范
- 工作标签必须使用方括号格式：`[开发]`, `[测试]`, `[文档]`等
- 禁止使用【】或其他格式的标签

## 内容描述规范
- 内容必须客观具体，禁止使用主观评价词
- 禁止使用"提升用户体验"、"优化"、"增强"、"提升"等主观评价词
- 使用"实现"、"完成"、"修复"等客观描述词
- 避免使用"更好"、"更快"、"更强"等比较级形容词
- 使用具体的数据和指标替代主观评价

