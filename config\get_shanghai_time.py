#!/usr/bin/env python
import datetime
import pytz

# 获取上海时区的当前时间
def get_shanghai_time():
    # 获取UTC时间
    utc_now = datetime.datetime.utcnow()
    # 定义上海时区
    shanghai_tz = pytz.timezone('Asia/Shanghai')
    # 将UTC时间转换为上海时间
    shanghai_time = utc_now.replace(tzinfo=pytz.utc).astimezone(shanghai_tz)
    # 返回格式化的上海时间
    return shanghai_time.strftime('%Y-%m-%d %H:%M:%S')

if __name__ == "__main__":
    shanghai_time = get_shanghai_time()
    print(shanghai_time) 