# 负载/接地切换按钮开发设计文档

## 1. 概述

### 1.1 项目背景
在T95电流检测模块中，用户需要能够灵活切换查看负载电流和接地电流数据。当前系统虽然同时采集两种电流数据，但缺乏便捷的切换显示机制。

### 1.2 功能目标
- 新增负载/接地切换按钮，支持用户在两种电流显示模式间切换
- 保持数据采集的连续性，仅改变显示内容
- 提供配置持久化，记住用户的选择偏好
- 确保与现有功能的兼容性

### 1.3 适用范围
- `CurrentDetectionView` (标准电流检测视图)
- `BJCurrentDetectionView` (北京版电流检测视图)
- 对应的图表组件 `CurrentDetectionChart` 和 `BJCurrentDetectionChart`

## 2. 系统架构设计

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户操作       │───▶│   视图层         │───▶│   图表组件       │
│  (按钮切换)      │    │ (View Classes)   │    │ (Chart Classes)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   配置管理       │◀───│   事件处理       │───▶│   数据处理       │
│ (Config System)  │    │ (Event Handler)  │    │ (Data Process)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 模块关系图

```mermaid
graph TB
    A[用户界面] --> B[CurrentDetectionView]
    A --> C[BJCurrentDetectionView]
    B --> D[CurrentDetectionChart]
    C --> E[BJCurrentDetectionChart]
    B --> F[ConfigManager]
    C --> F
    D --> G[数据显示逻辑]
    E --> G
    F --> H[配置持久化]
    
    subgraph "新增组件"
        I[CurrentDisplayType枚举]
        J[切换按钮配置]
        K[事件处理逻辑]
    end
    
    B --> I
    C --> I
    B --> J
    C --> J
    B --> K
    C --> K
```

## 3. 数据结构设计

### 3.1 核心枚举定义

```cpp
// 文件位置: Z200\module\currentdetection\currentdetectiondefine.h
namespace CurrentDetection
{
    // 电流显示类型
    enum CurrentDisplayType
    {
        CURRENT_DISPLAY_LOAD = 0,      // 显示负载电流
        CURRENT_DISPLAY_GROUNDING,     // 显示接地电流
        
        CURRENT_DISPLAY_MIN = CURRENT_DISPLAY_LOAD,
        CURRENT_DISPLAY_MAX = CURRENT_DISPLAY_GROUNDING,
        CURRENT_DISPLAY_DEFAULT = CURRENT_DISPLAY_LOAD,
    };
}
```

### 3.2 按钮配置结构

```cpp
// 文件位置: Z200\view\currentdetection\currentdetectionviewdefine.h
namespace CurrentDetection
{
    // 文本定义
    const char* const TEXT_CURRENT_DISPLAY_TYPE = QT_TRANSLATE_NOOP("CurrentDetectionView", "Current Type");
    const char* const TEXT_LOAD_CURRENT_DISPLAY = QT_TRANSLATE_NOOP("CurrentDetectionView", "Load Current");
    const char* const TEXT_GROUNDING_CURRENT_DISPLAY = QT_TRANSLATE_NOOP("CurrentDetectionView", "Grounding Current");

    // 电流显示类型选项
    const char* const TEXT_CURRENT_DISPLAY_OPTIONS[] =
    {
        TEXT_LOAD_CURRENT_DISPLAY,
        TEXT_GROUNDING_CURRENT_DISPLAY,
    };
}
```

### 3.3 配置管理结构

```cpp
// 文件位置: Z200\module\currentdetection\currentdetectionconfig.h
namespace CurrentDetection
{
    enum ConfigInfo
    {
        KEY_SAMPLE_MODE = 0,
        KEY_RANGE_GEAR,
        KEY_CURRENT_DISPLAY_TYPE,  // 新增配置项
    };

    static Config::KeyInfo KEYS_CURRENT_DETECTION_CONFIG[] =
    {
        {KEY_SAMPLE_MODE, "SampleMode", Config::NUMBER, QString::number(Module::SAMPLEMODE_DEFAULT), Module::SAMPLEMODE_MIN, Module::SAMPLEMODE_MAX},
        {KEY_RANGE_GEAR, "RangeGear", Config::NUMBER, QString::number(CurrentDetection::CURRENT_RANGE_GEAR_DEFAULT), CurrentDetection::CURRENT_RANGE_GEAR_MIN, CurrentDetection::CURRENT_RANGE_GEAR_MAX},
        {KEY_CURRENT_DISPLAY_TYPE, "CurrentDisplayType", Config::NUMBER, QString::number(CurrentDetection::CURRENT_DISPLAY_DEFAULT), CurrentDetection::CURRENT_DISPLAY_MIN, CurrentDetection::CURRENT_DISPLAY_MAX},
    };
}
```

## 4. 视图层设计

### 4.1 CurrentDetectionView 修改

#### 4.1.1 头文件修改 (currentdetectionview.h)

```cpp
class CurrentDetectionView : public SampleChartView
{
    Q_OBJECT

public:
    explicit CurrentDetectionView(QWidget *parent = NULL);
    virtual ~CurrentDetectionView();

private:
    CurrentDetectionChart* m_pChart;
    Module::SampleMode m_eSampleMode;
    CurrentDetection::CurrentRangeGear m_eCurrentRangeGear;
    CurrentDetection::CurrentDisplayType m_eCurrentDisplayType;  // 新增成员变量
    bool m_bIsSampling;

    /*************************************************
    函数名： loadCurrentDisplayTypeConfig()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 加载电流显示类型配置
    *************************************************************/
    void loadCurrentDisplayTypeConfig();

    /*************************************************
    函数名： saveCurrentDisplayTypeConfig()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 保存电流显示类型配置
    *************************************************************/
    void saveCurrentDisplayTypeConfig();
};
```

#### 4.1.2 按钮枚举修改

```cpp
// 文件位置: Z200\view\currentdetection\currentdetectionview.cpp
namespace
{
enum CurrentDetectionButton
{
    BUTTON_SAMPLE_MODE = 0,        // 采集模式
    BUTTON_SINGLE_SAMPLE,          // 单次采样
    BUTTON_CURRENT_DISPLAY_TYPE,   // 新增：电流显示类型切换
    BUTTON_SAVE_DATA,              // 保存数据
    BUTTON_MORE,                   // 更多配置
    BUTTON_RFID_SAVE,              // RFID扫描保存
    BUTTON_LOAD_DATA,              // 载入数据
    BUTTON_DELETE_DATA,            // 删除数据
    BUTTON_RANGE_GEAR,             // 量程档位
};

// 电流显示类型配置
const ButtonInfo::RadioValueConfig s_CurrentDisplayTypeCfg =
{
    CurrentDetection::TEXT_CURRENT_DISPLAY_OPTIONS, 
    sizeof(CurrentDetection::TEXT_CURRENT_DISPLAY_OPTIONS)/sizeof(char*)
};

// 控制按钮定义 (更新数组)
const ButtonInfo::Info s_CurrentDetectionButtonInfo[] =
{
    { BUTTON_SAMPLE_MODE, { ButtonInfo::RADIO, CurrentDetection::TEXT_MODE, NULL, ":/images/sampleControl/sampleMode.png", &s_SampleModeCfg } },
    { BUTTON_SINGLE_SAMPLE, { ButtonInfo::COMMAND, CurrentDetection::TEXT_SINGLE_SAMPLE, NULL, ":/images/sampleControl/sample.png", NULL } },
    { BUTTON_CURRENT_DISPLAY_TYPE, { ButtonInfo::RADIO, CurrentDetection::TEXT_CURRENT_DISPLAY_TYPE, NULL, ":/images/sampleControl/currentType.png", &s_CurrentDisplayTypeCfg } }, // 新增
    { BUTTON_RANGE_GEAR, { ButtonInfo::RADIO, CurrentDetection::TEXT_RANGE_GEAR, NULL, "", &s_RangeGearCfg } },
    // ... 其他按钮配置
};
}
```

### 4.2 BJCurrentDetectionView 修改

#### 4.2.1 按钮枚举修改

```cpp
// 文件位置: Z200\view\currentdetection\bjcurrentdetectionview.cpp
namespace
{
enum CurrentDetectionButton
{
    BUTTON_CURRENT_DETECTION_SAMPLE = 0,
    BUTTON_CURRENT_PHASE_SELECTION,
    BUTTON_CURRENT_DISPLAY_TYPE,        // 新增：电流显示类型切换
    BUTTON_CURRENT_DETECTION_SAVE_DATA,
    BUTTON_CURRENT_MORE,
    BUTTON_CURRENT_DETECTION_LOAD_DATA,
    BUTTON_CURRENT_DETECTION_DELETE_DATA,
};

// 控制按钮定义 (更新数组)
const ButtonInfo::Info s_CurrentDetectionButtonInfo[] =
{
    {BUTTON_CURRENT_DETECTION_SAMPLE, {ButtonInfo::COMMAND, CurrentDetection::TEXT_SAMPLE, NULL, ":/images/sampleControl/sample.png", NULL}},
    {BUTTON_CURRENT_PHASE_SELECTION, {ButtonInfo::RADIO, CurrentDetection::TEXT_PHASE_SELECTION, NULL, NULL, &s_CurrentPhaseSelection}},
    {BUTTON_CURRENT_DISPLAY_TYPE, {ButtonInfo::RADIO, CurrentDetection::TEXT_CURRENT_DISPLAY_TYPE, NULL, ":/images/sampleControl/currentType.png", &s_CurrentDisplayTypeCfg}}, // 新增
    {BUTTON_CURRENT_DETECTION_SAVE_DATA, {ButtonInfo::COMMAND, CurrentDetection::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL}},
    {BUTTON_CURRENT_MORE, {ButtonInfo::COMMAND, CurrentDetection::TEXT_MORE_CONFIG, NULL, ":/images/sampleControl/moreconfig.png", NULL}}
};
}
```

## 5. 事件处理设计

### 5.1 按钮值变化处理

```cpp
// CurrentDetectionView::onButtonValueChanged 方法扩展
void CurrentDetectionView::onButtonValueChanged(int id, int iValue)
{
    switch (id)
    {
    case BUTTON_SAMPLE_MODE:
    {
        Module::SampleMode eSampleMode = static_cast<Module::SampleMode>(iValue);
        if (eSampleMode != m_eSampleMode)
        {
            m_eSampleMode = eSampleMode;
            m_pChart->setSampleMode(m_eSampleMode);
        }
    }
        break;
    case BUTTON_CURRENT_DISPLAY_TYPE:  // 新增处理逻辑
    {
        CurrentDetection::CurrentDisplayType eNewType = static_cast<CurrentDetection::CurrentDisplayType>(iValue);
        if (eNewType != m_eCurrentDisplayType)
        {
            m_eCurrentDisplayType = eNewType;
            m_pChart->setCurrentDisplayType(m_eCurrentDisplayType);
            saveCurrentDisplayTypeConfig();
            
            logDebug(QString("Current display type changed to: %1").arg(iValue));
        }
    }
        break;
    case BUTTON_RANGE_GEAR:
    {
        CurrentDetection::CurrentRangeGear eRangeGear = static_cast<CurrentDetection::CurrentRangeGear>(iValue);
        if (eRangeGear != m_eCurrentRangeGear)
        {
            m_eCurrentRangeGear = eRangeGear;
            m_pChart->setCurrentRangeGear(m_eCurrentRangeGear);
        }
    }
        break;
    default:
        break;
    }
}
```

### 5.2 配置管理方法

```cpp
/*************************************************
函数名： loadCurrentDisplayTypeConfig()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 加载电流显示类型配置
*************************************************************/
void CurrentDetectionView::loadCurrentDisplayTypeConfig()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_CURRENT_DETECTION);
    
    int iDisplayType = pConfig->value(CurrentDetection::KEY_CURRENT_DISPLAY_TYPE).toInt();
    m_eCurrentDisplayType = static_cast<CurrentDetection::CurrentDisplayType>(iDisplayType);
    
    // 设置按钮初始值
    if (PushButtonBar* pButtonBar = buttonBar())
    {
        if (PopupButton* pButton = dynamic_cast<PopupButton*>(pButtonBar->button(BUTTON_CURRENT_DISPLAY_TYPE)))
        {
            pButton->setValue(static_cast<int>(m_eCurrentDisplayType));
        }
    }
    
    pConfig->endGroup();
}

/*************************************************
函数名： saveCurrentDisplayTypeConfig()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 保存电流显示类型配置
*************************************************************/
void CurrentDetectionView::saveCurrentDisplayTypeConfig()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_CURRENT_DETECTION);
    
    pConfig->setValue(static_cast<int>(m_eCurrentDisplayType), CurrentDetection::KEY_CURRENT_DISPLAY_TYPE);
    
    pConfig->endGroup();
}
```

## 6. 图表组件设计

### 6.1 CurrentDetectionChart 接口扩展

```cpp
// 文件位置: Z200\view\currentdetection\currentdetectionchart.h
class CurrentDetectionChart : public ChartWidget
{
    Q_OBJECT

public:
    /*************************************************
    函数名： setCurrentDisplayType(CurrentDetection::CurrentDisplayType eDisplayType)
    输入参数： eDisplayType：电流显示类型
    输出参数： NULL
    返回值： NULL
    功能： 设置电流显示类型
    *************************************************************/
    void setCurrentDisplayType(CurrentDetection::CurrentDisplayType eDisplayType);

    /*************************************************
    函数名： getCurrentDisplayType()
    输入参数： NULL
    输出参数： NULL
    返回值： CurrentDetection::CurrentDisplayType
    功能： 获取当前电流显示类型
    *************************************************************/
    CurrentDetection::CurrentDisplayType getCurrentDisplayType() const;

private:
    CurrentDetection::CurrentDisplayType m_eCurrentDisplayType;
    QLabel* m_pCurrentTypeLabel;  // 显示当前电流类型的标签

    /*************************************************
    函数名： updateCurrentTypeLabel()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 更新电流类型显示标签
    *************************************************************/
    void updateCurrentTypeLabel();
};
```

### 6.2 数据处理逻辑

```cpp
// 文件位置: Z200\view\currentdetection\currentdetectionchart.cpp

void CurrentDetectionChart::addSample(float fCurrentValue)
{
    // 根据显示类型处理数据
    float fDisplayValue = fCurrentValue;
    
    // 更新图表显示
    updateCurrentChart(fDisplayValue, getMaxData());
    
    // 更新类型标签
    updateCurrentTypeLabel();
}

void CurrentDetectionChart::setCurrentDisplayType(CurrentDetection::CurrentDisplayType eDisplayType)
{
    if (m_eCurrentDisplayType != eDisplayType)
    {
        m_eCurrentDisplayType = eDisplayType;
        
        // 清空历史数据，保留最大值
        clearHistoryDataOnly();
        
        // 更新显示标签
        updateCurrentTypeLabel();
        
        logDebug(QString("Chart display type changed to: %1").arg(static_cast<int>(eDisplayType)));
    }
}

void CurrentDetectionChart::updateCurrentTypeLabel()
{
    if (!m_pCurrentTypeLabel)
        return;
        
    QString strTypeText;
    switch (m_eCurrentDisplayType)
    {
    case CurrentDetection::CURRENT_DISPLAY_LOAD:
        strTypeText = CURRENT_DETECTION_VIEW_TRANSLATE(CurrentDetection::TEXT_LOAD_CURRENT_DISPLAY);
        break;
    case CurrentDetection::CURRENT_DISPLAY_GROUNDING:
        strTypeText = CURRENT_DETECTION_VIEW_TRANSLATE(CurrentDetection::TEXT_GROUNDING_CURRENT_DISPLAY);
        break;
    default:
        strTypeText = CURRENT_DETECTION_VIEW_TRANSLATE(CurrentDetection::TEXT_LOAD_CURRENT_DISPLAY);
        break;
    }
    
    m_pCurrentTypeLabel->setText(strTypeText);
}

## 7. 视图层时序图

### 7.1 按钮切换数据变化完整时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant View as CurrentDetectionView
    participant ButtonBar as PushButtonBar
    participant Chart as CurrentDetectionChart
    participant Config as ConfigManager
    participant Service as CurrentDetectionService
    participant Data as 电流数据

    Note over User,Data: 1. 初始化阶段
    User->>View: 启动电流检测界面
    View->>Config: loadCurrentDisplayTypeConfig()
    Config-->>View: 返回配置值(默认负载电流)
    View->>ButtonBar: 设置按钮初始值
    View->>Chart: setCurrentDisplayType(LOAD)
    Chart->>Chart: updateCurrentTypeLabel()

    Note over User,Data: 2. 数据采集阶段
    Service->>View: onDataRead(CurrentDetectionData)
    View->>Chart: addSample(data)

    alt 当前显示负载电流
        Chart->>Chart: 显示data.fLoadCurrentValue
        Chart->>Chart: updateCurrentChart(负载电流值)
    else 当前显示接地电流
        Chart->>Chart: 显示data.fGroundingCurrentValue
        Chart->>Chart: updateCurrentChart(接地电流值)
    end

    Note over User,Data: 3. 用户切换按钮
    User->>ButtonBar: 点击电流类型切换按钮
    ButtonBar->>View: onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, newValue)

    View->>View: 检查值是否变化
    alt 值发生变化
        View->>View: m_eCurrentDisplayType = newValue
        View->>Chart: setCurrentDisplayType(newValue)
        Chart->>Chart: clearHistoryDataOnly()
        Chart->>Chart: updateCurrentTypeLabel()
        View->>Config: saveCurrentDisplayTypeConfig()
        Config->>Config: 持久化保存配置

        Note right of View: 记录调试日志
        View->>View: logDebug("Current display type changed")
    else 值未变化
        View->>View: 忽略重复设置
    end

    Note over User,Data: 4. 后续数据处理
    loop 持续数据采集
        Service->>View: onDataRead(CurrentDetectionData)
        View->>Chart: addSample(data)

        alt 显示负载电流模式
            Chart->>Chart: 提取data.fLoadCurrentValue
            Chart->>Chart: updateCurrentChart(负载电流值)
            Chart->>Chart: 更新仪表盘显示
        else 显示接地电流模式
            Chart->>Chart: 提取data.fGroundingCurrentValue
            Chart->>Chart: updateCurrentChart(接地电流值)
            Chart->>Chart: 更新仪表盘显示

            alt 接地电流超过阈值
                Chart->>Chart: 设置红色报警显示
            else 接地电流正常
                Chart->>Chart: 设置绿色正常显示
            end
        end

        Chart->>Chart: updateCurrentTypeLabel()
        Chart->>Chart: 更新最大值显示
    end

    Note over User,Data: 5. 配置恢复阶段
    User->>View: 重新启动应用
    View->>Config: loadCurrentDisplayTypeConfig()
    Config-->>View: 返回上次保存的配置
    View->>ButtonBar: 恢复按钮状态
    View->>Chart: setCurrentDisplayType(保存的配置)
    Chart->>Chart: updateCurrentTypeLabel()
```

### 7.2 BJCurrentDetectionView 时序图差异

```mermaid
sequenceDiagram
    participant User as 用户
    participant BJView as BJCurrentDetectionView
    participant BJChart as BJCurrentDetectionChart
    participant PhaseBtn as 相别选择按钮
    participant TypeBtn as 类型切换按钮

    Note over User,TypeBtn: BJCurrentDetectionView 特有流程

    User->>PhaseBtn: 选择电流相别(A/B/C/N)
    PhaseBtn->>BJView: onButtonValueChanged(PHASE_SELECTION)
    BJView->>BJChart: 设置当前相别

    User->>TypeBtn: 切换电流显示类型
    TypeBtn->>BJView: onButtonValueChanged(CURRENT_DISPLAY_TYPE)
    BJView->>BJChart: setCurrentDisplayType(newType)

    alt 显示接地电流 + 相别选择
        BJChart->>BJChart: 显示选定相别的接地电流
        BJChart->>BJChart: updateGroundingCurrentText(value, phase)
    else 显示负载电流
        BJChart->>BJChart: 显示负载电流(不分相别)
        BJChart->>BJChart: updateLoadCurrentText(value)
    end
```

## 8. 数据流设计

### 8.1 数据流向图

```mermaid
flowchart TD
    A[硬件传感器] --> B[ZigBeeService]
    B --> C[PeripheralService]
    C --> D[CurrentDetectionService]
    D --> E[CurrentDetectionData]
    E --> F{用户选择显示类型}

    F -->|负载电流| G[fLoadCurrentValue]
    F -->|接地电流| H[fGroundingCurrentValue]

    G --> I[CurrentDetectionChart]
    H --> I

    I --> J[仪表盘显示]
    I --> K[历史曲线]
    I --> L[最大值显示]

    M[切换按钮] --> N[onButtonValueChanged]
    N --> O[setCurrentDisplayType]
    O --> P[updateCurrentTypeLabel]
    O --> Q[clearHistoryDataOnly]

    R[ConfigManager] --> S[配置加载]
    S --> T[按钮初始状态]
    N --> U[配置保存]
    U --> R
```

### 8.2 状态转换图

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 加载配置
    加载配置 --> 负载电流显示 : 默认状态
    加载配置 --> 接地电流显示 : 配置为接地电流

    负载电流显示 --> 接地电流显示 : 用户点击切换
    接地电流显示 --> 负载电流显示 : 用户点击切换

    负载电流显示 --> 负载电流显示 : 数据更新
    接地电流显示 --> 接地电流显示 : 数据更新

    负载电流显示 --> 保存配置 : 切换时
    接地电流显示 --> 保存配置 : 切换时
    保存配置 --> 负载电流显示
    保存配置 --> 接地电流显示
```

## 9. 错误处理设计

### 9.1 异常情况处理

```cpp
void CurrentDetectionView::onButtonValueChanged(int id, int iValue)
{
    switch (id)
    {
    case BUTTON_CURRENT_DISPLAY_TYPE:
    {
        // 参数有效性检查
        if (iValue < CurrentDetection::CURRENT_DISPLAY_MIN ||
            iValue > CurrentDetection::CURRENT_DISPLAY_MAX)
        {
            logError(QString("Invalid current display type value: %1").arg(iValue));
            return;
        }

        CurrentDetection::CurrentDisplayType eNewType = static_cast<CurrentDetection::CurrentDisplayType>(iValue);

        // 防止重复设置
        if (eNewType == m_eCurrentDisplayType)
        {
            logDebug("Current display type unchanged, ignoring.");
            return;
        }

        // 检查图表组件有效性
        if (!m_pChart)
        {
            logError("Chart component is null, cannot set display type.");
            return;
        }

        try
        {
            m_eCurrentDisplayType = eNewType;
            m_pChart->setCurrentDisplayType(m_eCurrentDisplayType);
            saveCurrentDisplayTypeConfig();

            logDebug(QString("Current display type changed to: %1").arg(iValue));
        }
        catch (const std::exception& e)
        {
            logError(QString("Exception in setCurrentDisplayType: %1").arg(e.what()));
            // 恢复原状态
            m_eCurrentDisplayType = (eNewType == CurrentDetection::CURRENT_DISPLAY_LOAD) ?
                                   CurrentDetection::CURRENT_DISPLAY_GROUNDING :
                                   CurrentDetection::CURRENT_DISPLAY_LOAD;
        }
    }
        break;
    }
}
```

### 9.2 配置加载失败处理

```cpp
void CurrentDetectionView::loadCurrentDisplayTypeConfig()
{
    try
    {
        ConfigInstance* pConfig = ConfigManager::instance()->config();
        if (!pConfig)
        {
            logError("ConfigManager instance is null, using default display type.");
            m_eCurrentDisplayType = CurrentDetection::CURRENT_DISPLAY_DEFAULT;
            return;
        }

        pConfig->beginGroup(Module::GROUP_CURRENT_DETECTION);

        bool bOk = false;
        int iDisplayType = pConfig->value(CurrentDetection::KEY_CURRENT_DISPLAY_TYPE).toInt(&bOk);

        if (!bOk || iDisplayType < CurrentDetection::CURRENT_DISPLAY_MIN ||
            iDisplayType > CurrentDetection::CURRENT_DISPLAY_MAX)
        {
            logWarning(QString("Invalid config value for display type: %1, using default.").arg(iDisplayType));
            m_eCurrentDisplayType = CurrentDetection::CURRENT_DISPLAY_DEFAULT;
        }
        else
        {
            m_eCurrentDisplayType = static_cast<CurrentDetection::CurrentDisplayType>(iDisplayType);
        }

        pConfig->endGroup();

        // 设置按钮初始值
        updateButtonDisplayType();
    }
    catch (const std::exception& e)
    {
        logError(QString("Exception in loadCurrentDisplayTypeConfig: %1").arg(e.what()));
        m_eCurrentDisplayType = CurrentDetection::CURRENT_DISPLAY_DEFAULT;
    }
}
```

## 10. 测试设计

### 10.1 单元测试用例

```cpp
// 测试用例1: 按钮切换功能
TEST_CASE("ButtonSwitchTest")
{
    CurrentDetectionView view;

    // 测试初始状态
    REQUIRE(view.getCurrentDisplayType() == CurrentDetection::CURRENT_DISPLAY_DEFAULT);

    // 测试切换到接地电流
    view.onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, CurrentDetection::CURRENT_DISPLAY_GROUNDING);
    REQUIRE(view.getCurrentDisplayType() == CurrentDetection::CURRENT_DISPLAY_GROUNDING);

    // 测试切换回负载电流
    view.onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, CurrentDetection::CURRENT_DISPLAY_LOAD);
    REQUIRE(view.getCurrentDisplayType() == CurrentDetection::CURRENT_DISPLAY_LOAD);
}

// 测试用例2: 配置持久化
TEST_CASE("ConfigPersistenceTest")
{
    {
        CurrentDetectionView view1;
        view1.onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, CurrentDetection::CURRENT_DISPLAY_GROUNDING);
        view1.saveCurrentDisplayTypeConfig();
    }

    {
        CurrentDetectionView view2;
        view2.loadCurrentDisplayTypeConfig();
        REQUIRE(view2.getCurrentDisplayType() == CurrentDetection::CURRENT_DISPLAY_GROUNDING);
    }
}

// 测试用例3: 异常值处理
TEST_CASE("InvalidValueTest")
{
    CurrentDetectionView view;
    CurrentDetection::CurrentDisplayType originalType = view.getCurrentDisplayType();

    // 测试无效值
    view.onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, -1);
    REQUIRE(view.getCurrentDisplayType() == originalType);

    view.onButtonValueChanged(BUTTON_CURRENT_DISPLAY_TYPE, 999);
    REQUIRE(view.getCurrentDisplayType() == originalType);
}
```

### 10.2 集成测试场景

1. **完整切换流程测试**
   - 启动应用 → 加载配置 → 切换按钮 → 验证显示 → 保存配置

2. **数据连续性测试**
   - 采集数据 → 切换显示类型 → 验证数据不丢失 → 验证显示正确

3. **多视图兼容性测试**
   - CurrentDetectionView 和 BJCurrentDetectionView 同时测试

4. **配置兼容性测试**
   - 旧版本配置文件兼容性验证

## 11. 部署和维护

### 11.1 版本兼容性

- **向后兼容**: 新增功能不影响现有配置文件
- **默认行为**: 未配置时默认显示负载电流
- **配置迁移**: 自动处理旧版本配置

### 11.2 性能考虑

- **内存使用**: 新增成员变量占用最小
- **CPU开销**: 切换操作为O(1)复杂度
- **响应时间**: 按钮响应时间<100ms

### 11.3 维护要点

- **日志记录**: 关键操作记录调试日志
- **错误处理**: 完善的异常捕获和恢复机制
- **代码规范**: 严格遵循现有编码标准
- **文档更新**: 及时更新用户手册和API文档

## 12. 总结

本设计文档详细描述了负载/接地切换按钮的完整实现方案，包括：

1. **架构设计**: 遵循现有MVC模式，最小化对现有代码的影响
2. **数据结构**: 新增枚举类型和配置项，保持向后兼容
3. **事件处理**: 完善的按钮事件处理和配置管理机制
4. **错误处理**: 全面的异常处理和恢复策略
5. **测试验证**: 完整的单元测试和集成测试方案

该方案确保了功能的可靠性、可维护性和可扩展性，为用户提供了便捷的电流数据查看体验。
