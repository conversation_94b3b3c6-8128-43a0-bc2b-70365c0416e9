# 红外相机新架构调试指南

## 🎯 当前状态

根据最新的日志分析，新的统一帧处理架构已经基本工作正常：

### ✅ 成功的部分
```
HikUSBAdapter: NV12 validation - Dimensions: 256 x 192 Y plane: 49152 UV plane: 24576 Expected total: 73728 Actual: 73728
HikUSBAdapter: NV12 data validation passed
HikUSBAdapter: Extracted temperature data - Size: 256 x 192 Range: 20 ~ 40 °C
HikUSBAdapter: Processed frame - Size: 256 x 192 Format: "NV12" DataSize: 73728
FrameProcessor: Frame processed successfully - Device: "HIK_USB" Size: 256 x 192 Format: "NV12" Time: 14 ms
HikInfraredModel: Unified frame processed - Device: "HIK_USB" Size: 256 x 192 Format: "NV12"
HikInfraredModel: Display image generated, size: QSize(256, 192)
```

### ❌ 剩余问题
1. **界面显示问题**：虽然图像处理成功，但界面可能没有正确显示
2. **重复错误**：`FrameProcessor: "No adapter registered for device type: 0"`

## 🔍 问题分析

### 问题1：界面显示流程
新架构的图像显示流程：
```
InfraredCamera -> HikInfraredModel -> FrameProcessor -> HikUSBAdapter -> QPixmap -> View
```

可能的问题点：
- `newFrameReady`信号没有正确连接到视图
- `InfraredImagingView`仍在等待`rawFrameDataReady`信号
- 图像格式转换问题

### 问题2：DeviceType_Unknown错误
这个错误表明某些地方传递了`DeviceType_Unknown`（值为0）。可能的来源：
- 传统处理路径仍在运行
- 某个地方没有正确设置设备类型
- 信号连接问题

## 🛠️ 调试步骤

### 步骤1：验证信号连接
检查以下信号是否正确连接：
```cpp
// 在HikInfraredPresenter中
connect(m_model, SIGNAL(newFrameReady(const QImage&)),
        this, SLOT(handleNewFrame(const QImage&)));

// 在HikInfraredPresenter中
connect(this, SIGNAL(newFrame(const QImage&)),
        m_view, SLOT(updateImage(const QImage&)));
```

### 步骤2：检查图像传递
在以下位置添加调试日志：
```cpp
// HikInfraredModel::onFrameProcessed
qDebug() << "Generated display image size:" << displayImage.size();
qDebug() << "Current frame size:" << m_currentFrame.size();

// HikInfraredPresenter::handleNewFrame
qDebug() << "Presenter received frame size:" << frame.size();

// HikInfraredView::updateImage
qDebug() << "View updating image size:" << image.size();
```

### 步骤3：验证适配器注册
添加调试信息检查适配器状态：
```cpp
// 在initializeFrameProcessor中
qDebug() << "Registered adapters count:" << m_frameProcessor->getAdapterCount();
qDebug() << "HikUSB adapter registered:" << m_frameProcessor->hasAdapter(DeviceType_HikUSB);
```

## 🎯 快速修复建议

### 修复1：确保信号正确发射
在`HikInfraredModel::onFrameProcessed`中：
```cpp
void HikInfraredModel::onFrameProcessed(const UnifiedFrameInfo& frameInfo) {
    // 生成显示图像
    QPixmap displayImage = m_frameProcessor->generateDisplayImage(frameInfo);
    if (!displayImage.isNull()) {
        m_currentFrame = displayImage.toImage();
        
        // 确保发射信号
        emit newFrameReady(m_currentFrame);
        
        qDebug() << "Emitted newFrameReady signal with size:" << m_currentFrame.size();
    }
}
```

### 修复2：禁用传统处理路径
确保在使用新架构时完全禁用传统处理：
```cpp
void HikInfraredModel::onInfraredFrameDataReceived(const FrameInfo& frameInfo) {
    if (m_useUnifiedFrameProcessing && m_frameProcessor) {
        // 只使用新架构，不发射任何传统信号
        m_frameProcessor->processFrameAsync(frameInfo.imageData, DeviceType_HikUSB, metadata);
        return; // 直接返回，不执行传统处理
    }
    
    // 传统处理（只在新架构禁用时执行）
    // ...
}
```

### 修复3：检查视图更新
在`HikInfraredView::updateImage`中添加调试：
```cpp
void HikInfraredView::updateImage(const QImage& image) {
    qDebug() << "HikInfraredView::updateImage called with size:" << image.size();
    
    if (image.isNull()) {
        qWarning() << "Received null image!";
        return;
    }
    
    // 更新显示
    // ...
    
    qDebug() << "Image updated successfully";
}
```

## 📊 性能监控

### 关键指标
- 帧处理时间：目前约14ms，性能良好
- 内存使用：监控QSharedPointer的使用
- 错误率：监控处理失败的帧数

### 监控代码
```cpp
// 在FrameProcessor中添加性能监控
void FrameProcessor::updateStats() {
    int processed, errors;
    double avgTime;
    getProcessingStats(processed, avgTime, errors);
    
    qDebug() << "Performance Stats:"
             << "Processed:" << processed
             << "Avg Time:" << avgTime << "ms"
             << "Error Rate:" << (errors * 100.0 / processed) << "%";
}
```

## 🎉 成功验证

当修复完成后，应该看到以下日志：
```
HikInfraredModel: Generated display image from unified frame, size: QSize(256, 192)
HikInfraredModel: Emitted newFrameReady and displayImageReady signals
HikInfraredPresenter: Received new frame, size: QSize(256, 192)
HikInfraredView: Updating image through MVP, size: QSize(256, 192)
```

并且不应该再看到：
```
FrameProcessor: "No adapter registered for device type: 0"
```

## 📝 总结

新架构的核心功能已经正常工作，主要问题在于信号连接和图像传递。通过上述调试步骤和修复建议，应该能够解决剩余的显示问题。
