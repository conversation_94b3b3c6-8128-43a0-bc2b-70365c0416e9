## 日期：2025-02-09

1. **蓝牙连接问题修复与验证 [维护]**
   - 工时：3小时
   - 修复了蓝牙连接失败的问题。根据复现步骤进行了测试，并通过打包验证确认问题已解决。

2. **测试沟通与自测内容确认 [沟通]**
   - 工时：1小时
   - 与测试沟通了测试报告内容，了解了自测应包含的内容。

3. **测点列表删除功能问题修复 [维护]**
   - 工时：2小时
   - 在自测过程中发现测点列表删除测点功能存在问题，及时定位并修复了该问题，验证删除操作正常执行。

4. **子任务状态更新问题修正 [维护]**
   - 工时：2小时
   - 修改了单个测点下发子任务状态未变化的问题，优化了状态更新逻辑。

## 日期：2025-02-10

1. **和测试沟通问题复现步骤 [沟通]**
   - 工时：1小时
   - 与宋波沟通，讨论了北京省检现场的问题的复现步骤。

2. **协助测试发现该问题，并验证问题消除 [测试]**
   - 工时：2小时
   - 协助宋波排查T95上传数据至桌面端，发现并验证问题已被消除。

3. **编辑测点合并子任务列表状态刷新的文档 [文档]**
   - 工时：1小时
   - 编辑并完善了测点合并子任务列表状态刷新的相关文档。

4. **协助测试排查T95上传录屏数据到桌面端数据动态两成问题 [测试]**
   - 工时：2小时
   - 协助宋波排查并解决T95上传录屏数据到桌面端数据动态两成的问题。

5. **排查测点数据上传，背景测点数据不现实问题 [维护]**
   - 工时：1.5小时
   - 排查并解决了测点数据上传过程中背景测点数据不显示的问题。

6. **修改测点删除，子任务状态变化问题 [维护]**
   - 工时：1小时
   - 修改了测点删除后子任务状态未正确变化的问题。

7. **修改4G状态展示问题 [维护]**
   - 工时：1小时
   - 修改了4G状态展示不正确的问题，调整后显示正常。

## 日期：2025-02-11

1. **编辑子任务的状态没有更新解决开发方案文档 [开发]**
   - 工时：1小时
   - 完成了编辑子任务状态未更新问题的解决方案文档。

2. **整理T95-V4.3.0.25-问题修复清单，和测试沟通问题复现步骤 [沟通]**
   - 工时：3小时
   - 整理了问题修复清单，并与宋波沟通了问题复现步骤。

3. **查看T95手持终端中的UHF、HFCT PRPS图谱中的动态量程功能，和董晓宇汇报该问题处理方案 [沟通]**
   - 工时：1小时
   - 查看了相关功能，并与董晓宇讨论了处理方案。

4. **排查4G启动问题和长时间不适用自动关闭的问题 [维护]**
   - 工时：2小时
   - 发现4G模块设置为休眠模式，长时间不使用会启动休眠模式，且开机会设置为关闭状态。

5. **T95手持终端自测 [测试]**
   - 工时：4小时
   - 普测任务自测：TEV、AE、UHF、HFCT
   - 普测内容：正常测试、单位切换、精度切换、数据上传至桌面端、测试文件载入、测试文件删除
   - 智能巡检任务测试

## 日期：2025-02-12

1. **T95手持终端自测 [测试]**
   - 工时：2小时
   - 使用嘉定平台移动APP与T95进行终端测试，测试TEV、AE、UHF、HFCT四种任务。

2. **泰岳终端外网连接调试 [测试]**
   - 工时：2小时
   - 与宋波合作调试泰岳终端的外网连接。

3. **子任务上传状态修复 [维护]**
   - 工时：2小时
   - 修复了子任务上传后未显示在已上传分类的问题，界面刷新失败。

4. **泰岳移动终端任务下发测试 [测试]**
   - 工时：2小时
   - 使用泰岳移动终端进行任务下发测试，并与负责人讨论任务下发失败问题，验证解决方案。

## 日期：2025-02-13

1. **排查T95手持终端与移动终端蓝牙通信终端通信中断问题 [维护]**
   - 工时：6小时
   - 针对T95手持终端与移动终端的蓝牙通信中断问题进行了排查，目前正在分析可能导致通信中断的因素，尚未找到具体解决方案。

2. **搭建蓝牙抓包工具环境 [学习]**
   - 工时：6小时
   - 尝试向驱动组的杨立兴学习搭建蓝牙抓包工具环境，由于环境搭建未成功，无法进一步分析蓝牙通信数据包的实际传输情况。

## 日期：2025-02-14

1. **优化测点列表页面状态的实时刷新机制 [优化]**
   - 工时：4小时
   - 完成了测点列表页面状态的实时刷新机制优化，确保移动终端下发测点数据后，T95手持终端测点列表同步更新，准确显示下发的测点信息。

2. **排查T95手持终端关闭蓝牙通信端口问题 [维护]**
   - 工时：2小时
   - 排查了T95手持终端关闭蓝牙通信端口的问题，发现与之前重庆的需求改动有关，并对问题进行了排查和代码方案调整。

3. **配合测试V4.3.0.25_Release.4固件包 [测试]**
   - 工时：3小时
   - 配合测试（宋波）对V4.3.0.25_Release.4固件包进行了全面测试，涉及到的模块有"接入终端任务下发、上传、重测、增测等功能点"。
   - 重复测试蓝牙连接断开，开关机连接蓝牙等操作。
   - 使用数据校验App进行了测试，校验了报文数据的正确性。

4. * [管理]**
   - 工时：1小时
   - 编辑了提测申请单，并对T95代码进行了整理，上传至git上，进行归纳整理。

## 日期：2025-02-15

1. **T95固件包V4.3.0.25_Release.4全面测试 [测试]**
   - 工时：3小时
   - 针对T95新打的固件包V4.3.0.25_Release.4进行了接入终端全面测试。
   - 使用数字化仪器通讯和数据校验工具通信正常，字段校验通过。
   - 使用泰岳移动终端的调试正常，测试通过。

2. **排查固件包升级问题和4G自启动问题 [维护]**
   - 工时：2小时
   - 配合杨洪波排查固件包升级存在的问题和4G自启动问题。

3. **输出开发测试报告文档 [文档]**
   - 工时：1小时
   - 输出了开发测试报告文档，并将文件放到了File/20250215文件夹目录下。

## 日期：2025-02-17

1. **搭建nRF52840开发板抓蓝牙数据包环境 [开发]**
   - 工时：2小时
   - 搭建nRF52840开发板环境，查看T95与移动终端蓝牙通信报文。

2. **梳理江苏数字化接入终端合并主分支设计思路 [设计]**
   - 工时：2小时
   - 整理并梳理江苏数字化接入终端合并主分支的相关设计思路，输出需要合并的相关类清单。

3. **整理重复类对象命名问题 [优化]**
   - 工时：1小时
   - 整理并解决重复类对象的命名问题。

4. **重构SubTask对象 [重构]**
   - 工时：4小时
   - 重构原先的SubTask对象，该对象用于管理XML文件，负责测试数据的编辑和保存。

## 日期：2025-02-18

1. **修改和完善任务管理对象DigitalTask [开发]**
   - 工时：3小时
   - 参考SubTask对象，对DigitalTask对象进行了修改和优化，将原来的QVector改为了QHash，提升了任务分配和管理的效率和准确性。

2. **修改和完善数据管理对象DigitalTaskManager [开发]**
   - 工时：3小时
   - 参考TaskManager对象，对DigitalTaskManager对象的功能进行了修改和优化，特别是在数据存储和检索方面，将原来的QVector改为了QHash。在模拟大量数据下，查询功能时间明显减少。

3. **修改和完善视图对象AddNewTestView [开发]**
   - 工时：3.5小时
   - 改进了AddNewTestView的界面和交互逻辑。

## 日期：2025-02-19

1. **帮助解决工具打包和程序运行闪退问题 [支持]**
   - 工时：2小时
   - 协助胡江浪解决工具打包问题和程序运行闪退问题。

2. **完成工具安装打包和解决中文目录乱码问题 [开发]**
   - 工时：2小时
   - 完成了工具安装打包工作，并解决了软件中文目录乱码的问题。

3. **修改和完善数据管理对象DigitalTaskManager功能 [开发]**
   - 工时：6.5小时
   - 继续完成DigitalTaskManager对象功能的修改和完善，并调试接入终端任务下载功能。

## 日期：2025-02-20

1. **任务类型选择页面开发 [开发]**
   - 工时：4小时
   - 开发了任务类型选择页面。

2. **UHF无SubTask对象模式下测点保存功能调试 [开发]**
   - 工时：3小时
   - 调试UHF无SubTask对象模式下的测点保存功能。

3. **测点保存功能问题排查 [调试]**
   - 工时：2.5小时
   - 排查测点无法写进XML文件的问题。

4. **测点保存功能问题解决方案设计 [设计]**
   - 工时：2小时
   - 设计解决方案以解决测点保存功能的问题。

## 日期：2025-02-21

1. **接入终端任务功能调试 [调试]**
   - 工时：4小时
   - 调试完成接入终端任务下发、解析、读取、展示、保存等功能。

2. **间隔列表页面和测点列表页面开发 [开发]**
   - 工时：3.5小时
   - 完成间隔列表页面和测点列表页面的开发。

3. **协助编译动态库和问题排查 [支持]**
   - 工时：1小时
   - 协助谢兴飞编译动态库，并排查问题原因。

## 日期：2025-02-24

1. **复制开发超声类对象**
   - 分析了现有的超声类对象代码，确定了需要支持的图谱数据类型。
   - 创建了新的类或模块以支持超声幅值、相位、飞行等图谱数据的处理。
   - 工时：2小时

2. **编译超声数据功能**
   - 检查了超声数据的编译流程，确保各个模块的正常运行。
   - 确保超声数据的编译功能正常，包括幅值、相位和飞行时间等图谱数据。
   - 工时：3.5小时

3. **校验AE dat测点数据**
   - 核对并验证了保存的AE dat测点数据，确保数据的完整性和准确性。
   - 工时：1小时

4. **调试和数据报文检查**
   - 使用调试工具排查超声数据处理中的问题，确保数据报文的正确性。
   - 工时：2.5小时

5. **协助编译动态库**
   - 协助谢兴飞进行动态库的编译，排查编译过程中出现的问题。
   - 工时：1小时

## 日期：2025-02-25

1. **复制TEV类对象开发**
   - 分析了原先地点波TEV类对象代码，创建了新的类或模块以支持相关功能。
   - 工时：2小时

2. **调通TEV幅值图谱数据采集功能**
   - 检查了TEV幅值图谱数据采集功能的实现，确保其正常运行。
   - 工时：3.5小时

3. **核对TEV dat测点数据**
   - 核对并验证了保存的TEV dat测点数据，确保数据的完整性和准确性。
   - 工时：1小时

4. **消除调试问题**
   - 使用调试工具排查数据处理中的问题，确保系统稳定性。
   - 工时：2.5小时

5. **协助编译动态库**
   - 协助谢兴飞进行动态库的编译，并排查编译过程中出现的问题。
   - 工时：2小时

## 日期：2025-02-26

1. **T95软著相关材料完善 [文档]**
   - 整理和完善了T95软件著作权相关材料。
   - 工时：1.5小时

2. **复制开发UHF PRPS类对象 [开发]**
   - 分析了原先UHF PRPS类对象代码，创建了新的类或模块以支持相关功能。
   - 工时：2小时

3. **调通UHF PRPS图谱数据采集功能 [开发]**
   - 调试并完成了UHF PRPS图谱数据采集功能。
   - 适配了新版调理器采集功能。
   - 工时：2小时

4. **核对UHF dat测点数据 [测试]**
   - 核对并验证了保存的UHF dat测点数据，确保数据的完整性和准确性。
   - 工时：1.5小时

5. **消除调试问题 [调试]**
   - 使用调试工具排查并解决了数据处理中的问题。
   - 工时：1小时

6. **排查莲花池T95连接关机问题 [维护]**
   - 针对莲花池现场反馈的T95连接总是关机问题进行排查和分析。
   - 工时：4小时

7. **梳理本月工作内容及材料 [管理]**
   - 整理和总结了本月的工作内容和相关材料。
   - 工时：0.5小时

## 日期：2025-02-27

1. **完善UHF PRPS功能开发 [开发]**
   - 对UHF PRPS功能进行了进一步的开发和完善，确保其功能的完整性。
   - 工时：2小时

2. **跟进莲花池现场问题解决情况 [维护]**
   - 根据现场反馈的日志内容，尝试复现莲花池T95连接关机问题，并进行分析。
   - 工时：2小时

3. **进行UHF PRPS图谱功能测试和验证 [测试]**
   - 对UHF PRPS图谱功能进行了全面的测试和验证，确保数据采集的准确性。
   - 工时：2小时

4. **根据原先HFCT PRPS类对象代码进行复制开发 [开发]**
   - 复制并开发了HFCT PRPS类对象，确保新功能的实现。
   - 工时：2小时

5. **调通HFCT PRPS图谱数据采集功能 [开发]**
   - 调试并适配了HFCT PRPS图谱数据采集功能，确保与新版调理器的兼容性。
   - 工时：2小时

6. **核对保存的HFCT dat测点数据 [测试]**
   - 核对并验证了保存的HFCT dat测点数据，确保数据的完整性和准确性。
   - 工时：1小时

7. **消除调试问题 [调试]**
   - 使用调试工具排查并解决了数据处理中的问题，确保系统的稳定性。
   - 工时：1小时

## 日期：2025-02-28

1. **图谱数据功能测试 [测试]**
   - 测试并调整了AE、TEV、UHF、HFCT四种数据图谱的功能准确性。
   - 工时：3小时

2. **平台数据上传验证 [测试]**
   - 在测试环境中进行数据上传，验证平台功能的正确性。
   - 工时：2小时

3. **子任务列表功能优化 [开发]**
   - 分析并修改了普测模式下任务选择的设计问题。
   - 优化了子任务列表的功能逻辑。
   - 工时：2.5小时

4. **国际化翻译工作 [开发]**
   - 针对新增词汇进行了国际化翻译工作。
   - 工时：1小时

5. **月度工作内容梳理 [管理]**
   - 整理和总结了2月份的相关工作内容和材料。
   - 工时：2小时

6. **下月工作规划 [管理]**
   - 梳理和规划了3月份的工作计划和安排。
   - 工时：1小时

7. **周工作总结 [管理]**
   - 完成了本周工作总结。
   - 制定了下周工作计划。
   - 工时：1小时


   