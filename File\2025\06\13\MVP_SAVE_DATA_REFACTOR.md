# 保存数据功能MVP架构重构

## 🎯 **重构目标**

将`HikInfraredView::saveData(bool bSaveJpeg)`中的业务逻辑重构到MVP架构中，实现职责分离。

## 📋 **当前状态**

### **原始实现问题**
- `HikInfraredView::saveData()` 方法被完全注释掉
- 所有业务逻辑都在View层处理，违反MVP架构原则
- 包含：数据获取、配置读取、温度转换、文件保存等业务逻辑

### **硬件限制**
- 当前红外镜头无法获取温度数据
- 需要为将来的功能扩展预留接口

## 🚀 **重构方案**

### **架构分层**

#### **View层** - `HikInfraredView::saveData(bool bSaveJpeg)`
- **职责**：只处理UI交互
- **功能**：显示文件备注对话框、进度提示、用户反馈
- **调用**：`m_pPresenter->onSaveData(remark, bSaveJpeg)`

#### **Presenter层** - `HikInfraredPresenter::onSaveData(bool bSaveJpeg)`
- **职责**：协调保存流程，管理UI状态
- **功能**：处理异步操作、错误处理、用户反馈
- **调用**：`m_pModel->saveInfraredData(remark, bSaveJpeg, savedFilePath)`

#### **Model层** - `HikInfraredModel::saveInfraredData(...)`
- **职责**：执行完整的数据保存业务逻辑
- **功能**：数据收集、配置读取、温度转换、文件保存

## 🔧 **已实现的功能**

### **在HikInfraredModel中新增的方法**

#### 1. **数据获取方法**
```cpp
/**
 * @brief 获取当前红外原始数据
 * @param rawDataBuf 原始数据缓存
 * @param len 数据长度
 * @param frameInfo 帧数据信息
 * @param stParams 红外参数信息
 * @return true成功，false失败
 */
bool getCurrentInfraredData(unsigned short* rawDataBuf, quint32& len, 
                           FrameInfo& frameInfo, Params& stParams);
```

#### 2. **温度信息获取**
```cpp
/**
 * @brief 获取当前温度信息
 * @return 温度信息结构体
 */
TemperatureInfo getCurrentTemperatureInfo();
```

#### 3. **参考温度获取**
```cpp
/**
 * @brief 获取参考温度
 * @return 参考温度值
 */
float getReferenceTemperature();
```

#### 4. **调色板类型获取**
```cpp
/**
 * @brief 获取当前调色板类型
 * @return 调色板类型
 */
ColorType getCurrentColorType();
```

#### 5. **完整保存方法**
```cpp
/**
 * @brief 保存红外数据（完整的业务逻辑）
 * @param remark 文件备注
 * @param saveJpeg 是否同时保存JPEG格式
 * @param savedFilePath 保存的文件路径（输出参数）
 * @return true成功，false失败
 */
bool saveInfraredData(const QString& remark, bool saveJpeg, QString& savedFilePath);
```

## 💡 **当前实现特点**

### **占位符实现**
由于当前红外镜头无法获取温度，所有方法都是占位符实现：

```cpp
// 示例：getCurrentTemperatureInfo()
TemperatureInfo HikInfraredModel::getCurrentTemperatureInfo()
{
    // TODO: 当前红外镜头无法获取温度，功能暂时空着
    TemperatureInfo tempInfo;
    memset(&tempInfo, 0, sizeof(TemperatureInfo));
    
    // 设置默认值
    tempInfo.max = 0.0f;
    tempInfo.min = 0.0f;
    tempInfo.avg = 0.0f;
    tempInfo.center = 0.0f;
    
    qDebug() << "HikInfraredModel: getCurrentTemperatureInfo - function placeholder, waiting for hardware support";
    return tempInfo;
}
```

### **配置支持**
- 支持从配置文件读取参考温度
- 支持调色板类型管理
- 为将来的功能扩展做好准备

### **调试信息**
- 每个方法都有详细的调试日志
- 明确标识当前为占位符实现
- 便于将来的功能开发和调试

## 📈 **下一步计划**

### **1. 完善Presenter层**
- 实现`HikInfraredPresenter::onSaveData()`方法
- 处理异步保存流程
- 管理UI状态和用户反馈

### **2. 简化View层**
- 重写`HikInfraredView::saveData()`方法
- 只保留UI交互逻辑
- 调用Presenter处理业务

### **3. 完善Model层**
- 当硬件支持温度获取时，完善数据获取方法
- 实现完整的`saveInfraredData()`业务逻辑
- 集成现有的`pfnSaveDataFun`保存函数

### **4. 测试和验证**
- 创建单元测试
- 验证MVP架构的正确性
- 确保向后兼容性

## 🎯 **架构优势**

### **职责分离**
- View层：纯UI逻辑
- Presenter层：协调和状态管理
- Model层：业务逻辑和数据管理

### **可扩展性**
- 为将来的温度功能预留接口
- 支持不同的保存格式和配置
- 易于添加新的业务逻辑

### **可测试性**
- 每层都可以独立测试
- 业务逻辑与UI分离
- 便于模拟和单元测试

### **可维护性**
- 清晰的代码结构
- 明确的职责划分
- 易于理解和修改

## 🔮 **将来的扩展**

当红外镜头支持温度获取时，只需要：

1. **更新Model层方法实现**：
   - 从统一帧处理架构获取实际数据
   - 实现真正的温度数据处理

2. **保持接口不变**：
   - View和Presenter层无需修改
   - 完美的向前兼容

3. **增强功能**：
   - 添加更多的数据处理选项
   - 支持更多的保存格式
   - 实现高级的温度分析功能

这个重构为红外相机模块的数据保存功能奠定了坚实的架构基础！
