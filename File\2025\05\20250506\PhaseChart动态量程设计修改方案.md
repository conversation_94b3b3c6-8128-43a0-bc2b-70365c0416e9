# PhaseChart动态量程设计修改方案

## 1. 设计目标

*   允许用户设置固定量程上下限
    
*   支持动态上限调整
    
*   确保动态上限不影响数据值的变化
    
*   优化用户界面交互体验
    

## 2. 关键变量定义

*   `m_fUpperBound`: 量程上限值，类型为 Phase::ValueType (float)
    
*   `m_fLowerBound`: 量程下限值，类型为 Phase::ValueType (float)
    
*   `m_bDynamicDisplayRange`: 动态显示量程开关，类型为 bool
    

## 3. 接口函数定义

### 3.1 量程范围设置接口

*   `setDataProcessRange(min, max)`: 设置数据处理范围
    
*   `setDisplayRange(min, max)`: 设置显示范围
    
*   `setUpperBound(max)`: 设置量程上限
    
*   `setLowerBound(min)`: 设置量程下限
    

### 3.2 动态量程控制接口

*   `isDynamicDisplayRange()`: 获取动态显示量程状态
    
*   `setDynamicDisplayRange(enabled)`: 设置是否启用动态显示量程
    

### 3.3 振幅变化响应接口

*   `onMaxAmplitudeChange(maxAmplitude)`: 处理最大振幅变化的回调函数
    

## 4. 数据类型定义

*   `Phase::ValueType`: 量程值的数据类型，定义为 float
    

## 5. 使用说明

### 5.1 固定量程设置

1.  调用 `setDynamicDisplayRange(false)` 禁用动态量程
    
2.  使用 `setUpperBound()` 和 `setLowerBound()` 设置固定量程范围
    

### 5.2 动态量程设置

1.  调用 `setDynamicDisplayRange(true)` 启用动态量程
    
2.  系统会自动通过 `onMaxAmplitudeChange()` 响应最大振幅变化
    
3.  动态量程仅影响显示范围，不会改变数据处理范围
    

### 5.3 数据处理范围设置

1.  使用 `setDataProcessRange()` 设置数据处理范围
    
2.  此范围独立于显示范围，用于数据采集和处理
    

## 6. 实现注意事项

### 6.1 数据一致性

*   确保数据处理范围和显示范围的一致性
    
*   动态量程变化时保持数据值的稳定性
    

### 6.2 性能优化

*   避免频繁更新量程范围
    
*   优化动态量程调整的响应速度
    

### 6.3 用户体验

*   提供清晰的量程状态指示
    
*   确保量程调整操作的流畅性
    

## 7. 测试要点

### 7.1 功能测试

*   固定量程设置的正确性
    
*   动态量程调整的响应性
    
*   数据处理范围的独立性
    

### 7.2 性能测试

*   动态量程调整的响应时间
    
*   大量数据下的稳定性
    

### 7.3 兼容性测试

*   不同数据量下的表现
    
*   与其他功能的协同性
    

## 8. 实现细节

### 8.1 类成员变量

```cpp
class PhaseChartView {
private:
    Phase::ValueType m_fUpperBound;        // 量程上限
    Phase::ValueType m_fLowerBound;        // 量程下限
    bool m_bDynamicDisplayRange;           // 动态显示量程开关
    Phase::ValueType m_fProcessUpperBound; // 数据处理上限
    Phase::ValueType m_fProcessLowerBound; // 数据处理下限
};
```

### 8.2 关键函数实现

```cpp
// 设置动态显示量程
void PhaseChartView::setDynamicDisplayRange(bool enabled)
{
    if (m_bDynamicDisplayRange != enabled) {
        m_bDynamicDisplayRange = enabled;
        if (!enabled) {
            // 禁用动态量程时，恢复固定量程设置
            setDisplayRange(m_fProcessLowerBound, m_fProcessUpperBound);
        }
        update();
    }
}

// 响应最大振幅变化
void PhaseChartView::onMaxAmplitudeChange(Phase::ValueType fMaxAmplitude)
{
    if (m_bDynamicDisplayRange) {
        // 动态调整显示范围，保持一定余量
        Phase::ValueType newUpperBound = fMaxAmplitude * 1.2f;
        if (newUpperBound > m_fProcessUpperBound) {
            newUpperBound = m_fProcessUpperBound;
        }
        setDisplayRange(m_fProcessLowerBound, newUpperBound);
    }
}

// 设置数据处理范围
void PhaseChartView::setDataProcessRange(Phase::ValueType min, Phase::ValueType max)
{
    m_fProcessLowerBound = min;
    m_fProcessUpperBound = max;
    
    if (!m_bDynamicDisplayRange) {
        // 非动态模式下，同时更新显示范围
        setDisplayRange(min, max);
    }
}
```

### 8.3 信号与槽连接

```cpp
// 在构造函数中建立信号槽连接
connect(m_pModel, &PhaseDataModel::maxAmplitudeChanged,
        this, &PhaseChartView::onMaxAmplitudeChange);
```

## 9. 使用示例

### 9.1 初始化设置

```cpp
// 创建视图对象
PhaseChartView* pView = new PhaseChartView();

// 设置数据处理范围
pView->setDataProcessRange(0.0f, 100.0f);

// 设置初始显示范围
pView->setDisplayRange(0.0f, 50.0f);

// 启用动态量程
pView->setDynamicDisplayRange(true);
```

### 9.2 动态量程控制

```cpp
// 禁用动态量程
pView->setDynamicDisplayRange(false);

// 设置固定量程范围
pView->setUpperBound(80.0f);
pView->setLowerBound(0.0f);

// 重新启用动态量程
pView->setDynamicDisplayRange(true);
```

## 10. 注意事项

### 10.1 性能考虑

*   动态量程调整时避免频繁重绘
    
*   使用定时器控制更新频率
    
*   考虑使用双缓冲绘图
    

### 10.2 内存管理

*   注意数据缓存的大小控制
    
*   及时清理不需要的数据
    

### 10.3 线程安全

*   确保数据访问的线程安全
    
*   使用互斥锁保护共享资源
    

## 11. PrpsChart和PrpdChart修改方案

### 11.1 修改原因

PrpsChart和PrpdChart是绘制图表的核心类，需要修改以支持动态量程功能：

*   两个类都继承自PhaseChart基类，该基类已包含动态量程相关变量
    
*   目前量程设置只影响PhasePaintData，没有真正影响到图表绘制
    
*   onMaxAmplitudeChange方法实现不完整，无法实现动态量程调整
    

### 11.2 PrpsChart类修改

```cpp
class PrpsChart : public PhaseChart {
public:
    // 已有方法，需完善实现
    void setDataProcessRange(Phase::ValueType min, Phase::ValueType max);
    void setDisplayRange(Phase::ValueType min, Phase::ValueType max);
    
    // 新增方法
    bool isDynamicDisplayRange() const;
    void setDynamicDisplayRange(bool enabled);
    
private:
    // 调整绘图逻辑以使用新的量程范围
    void updateDrawingWithCurrentRange();
};
```

实现示例：

```cpp
void PrpsChart::setDataProcessRange(Phase::ValueType min, Phase::ValueType max)
{
    m_fDataProcessMin = min;
    m_fDataProcessMax = max;
    
    if (!m_bDynamicDisplayRange) {
        // 非动态模式下，同时更新显示范围
        setDisplayRange(min, max);
    }
    
    updateParams();
}

void PrpsChart::setDisplayRange(Phase::ValueType min, Phase::ValueType max)
{
    m_fDisplayMin = min;
    m_fDisplayMax = max;
    
    // 更新绘图配置
    updateParams();
}

bool PrpsChart::isDynamicDisplayRange() const
{
    return m_bDynamicDisplayRange;
}

void PrpsChart::setDynamicDisplayRange(bool enabled)
{
    if (m_bDynamicDisplayRange != enabled) {
        m_bDynamicDisplayRange = enabled;
        if (!enabled) {
            // 禁用动态量程时，恢复处理量程为显示量程
            setDisplayRange(m_fDataProcessMin, m_fDataProcessMax);
        }
    }
}
```

### 11.3 PrpdChart类修改

```cpp
class PrpdChart : public QObject, public PhaseChart {
    Q_OBJECT
    
public:
    // 需要新增的方法
    void setDataProcessRange(Phase::ValueType min, Phase::ValueType max);
    void setDisplayRange(Phase::ValueType min, Phase::ValueType max);
    bool isDynamicDisplayRange() const;
    void setDynamicDisplayRange(bool enabled);
    
private:
    // 调整绘图逻辑以使用新的量程范围
    void updateDrawingWithCurrentRange();
};
```

实现示例：

```cpp
void PrpdChart::setDataProcessRange(Phase::ValueType min, Phase::ValueType max)
{
    m_fDataProcessMin = min;
    m_fDataProcessMax = max;
    
    if (!m_bDynamicDisplayRange) {
        // 非动态模式下，同时更新显示范围
        setDisplayRange(min, max);
    }
    
    updateParams();
}

void PrpdChart::setDisplayRange(Phase::ValueType min, Phase::ValueType max)
{
    m_fDisplayMin = min;
    m_fDisplayMax = max;
    
    // 更新绘图配置
    updateParams();
}

bool PrpdChart::isDynamicDisplayRange() const
{
    return m_bDynamicDisplayRange;
}

void PrpdChart::setDynamicDisplayRange(bool enabled)
{
    if (m_bDynamicDisplayRange != enabled) {
        m_bDynamicDisplayRange = enabled;
        if (!enabled) {
            // 禁用动态量程时，恢复处理量程为显示量程
            setDisplayRange(m_fDataProcessMin, m_fDataProcessMax);
        }
    }
}
```

### 11.4 PhaseChartView类中的配套修改

```cpp
void PhaseChartView::setDataProcessRange(Phase::ValueType min, Phase::ValueType max)
{
    m_fProcessLowerBound = min;
    m_fProcessUpperBound = max;
    
    // 同时修改子图表的处理范围
    if (m_pPrpsChart) {
        m_pPrpsChart->setDataProcessRange(min, max);
    }
    
    if (m_pPrpdChart) {
        m_pPrpdChart->setDataProcessRange(min, max);
    }
    
    if (!m_bDynamicDisplayRange) {
        // 非动态模式下，同时更新显示范围
        setDisplayRange(min, max);
    }
}

void PhaseChartView::setDisplayRange(Phase::ValueType min, Phase::ValueType max)
{
    // 更新子图表的显示范围
    if (m_pPrpsChart) {
        m_pPrpsChart->setDisplayRange(min, max);
    }
    
    if (m_pPrpdChart) {
        m_pPrpdChart->setDisplayRange(min, max);
    }
    
    // 更新绘制数据的量程范围
    if (m_pPaintData) {
        m_pPaintData->setDataLowerBound(min);
        m_pPaintData->setDataUpperBound(max);
    }
    
    updateChartParams();
    updateChartContens();
}

void PhaseChartView::setDynamicDisplayRange(bool enabled)
{
    if (m_bDynamicDisplayRange != enabled) {
        m_bDynamicDisplayRange = enabled;
        
        // 同步到子图表
        if (m_pPrpsChart) {
            m_pPrpsChart->setDynamicDisplayRange(enabled);
        }
        
        if (m_pPrpdChart) {
            m_pPrpdChart->setDynamicDisplayRange(enabled);
        }
        
        if (!enabled) {
            // 禁用动态量程时，恢复固定量程设置
            setDisplayRange(m_fProcessLowerBound, m_fProcessUpperBound);
        }
        
        update();
    }
}

void PhaseChartView::onMaxAmplitudeChange(Phase::ValueType fMaxAmplitude)
{
    if (m_pPaintData) {
        m_pPaintData->setMaxAmplitude(fMaxAmplitude);
        
        // 添加动态量程调整逻辑
        if (m_bDynamicDisplayRange) {
            // 计算合适的显示范围，保留一定余量
            Phase::ValueType newUpperBound = fMaxAmplitude * 1.2f;
            if (newUpperBound > m_fProcessUpperBound) {
                newUpperBound = m_fProcessUpperBound;
            }
            setDisplayRange(m_fProcessLowerBound, newUpperBound);
        }
    }
}
```

### 11.5 实现注意事项

1.  确保两个图表类的量程逻辑一致，避免数据显示不一致
    
2.  绘图时应使用显示量程(m\_fDisplayMin/Max)而非处理量程
    
3.  处理量程变更和动态量程开关时需要同步所有相关对象
    
4.  动态量程变化仅应影响显示，不应影响数据处理和存储