# 随机电流数据与自动量程切换测试指南

## 📊 随机数据生成

### 修改内容
已将 `PeripheralService::readCurrentDetectionData()` 函数修改为生成随机电流值：

```cpp
int PeripheralService::readCurrentDetectionData(CableCurrentData* pstCableCurrentData)
{
    // 生成0-5000A范围的随机电流值
    static bool seedInitialized = false;
    if (!seedInitialized) {
        qsrand(QTime::currentTime().msec());
        seedInitialized = true;
    }
    
    // 生成随机的接地电流值 (0-5000A)
    float fGroundCurrentRandom = (qrand() % 50001) / 10.0f;  // 0.0 - 5000.0A
    
    // 生成随机的负荷电流值 (0-5000A)  
    float fLoadCurrentRandom = (qrand() % 50001) / 10.0f;    // 0.0 - 5000.0A
    
    pstCableCurrentData->fGroundCurrent = fGroundCurrentRandom;
    pstCableCurrentData->fLoadCurrent = fLoadCurrentRandom;
    
    return 0;
}
```

### 数据特性
- **范围**: 0.0A - 5000.0A
- **精度**: 0.1A (一位小数)
- **分布**: 均匀随机分布
- **更新频率**: 根据采样模式更新

## 🎯 自动量程切换测试

### 测试场景设计

#### 场景1: 小电流测试 (0-8A)
- **预期档位**: 10A
- **测试方法**: 观察随机值在0-8A范围时的表现
- **验证点**: 
  - 仪表盘量程显示0-10A
  - 指针位置正确
  - 中心数值显示准确

#### 场景2: 中等电流测试 (8-400A)  
- **预期档位**: 500A
- **测试方法**: 观察随机值在8-400A范围时的表现
- **验证点**:
  - 自动从10A档位切换到500A档位
  - 仪表盘量程显示0-500A
  - 切换过程平滑无闪烁

#### 场景3: 大电流测试 (400-5000A)
- **预期档位**: 5000A  
- **测试方法**: 观察随机值在400-5000A范围时的表现
- **验证点**:
  - 自动切换到5000A档位
  - 仪表盘量程显示0-5000A
  - 超大电流值正确显示

#### 场景4: 跨档位切换测试
- **测试方法**: 长时间运行，观察各档位间的切换
- **验证点**:
  - 10A ↔ 500A 切换正常
  - 500A ↔ 5000A 切换正常  
  - 10A → 5000A 跨档切换正常
  - 防抖机制工作正常

## 🧪 测试步骤

### 1. 启动应用测试
```bash
1. 编译并运行应用
2. 进入电流检测界面
3. 选择"自动"档位
4. 选择"连续"采样模式
5. 开始采样
```

### 2. 观察要点

#### 自动切换行为
- [ ] 随机值 ≤ 8A 时，使用10A档位
- [ ] 随机值 8-400A 时，使用500A档位  
- [ ] 随机值 > 400A 时，使用5000A档位

#### 界面表现
- [ ] 仪表盘量程自动调整
- [ ] 指针位置准确反映电流值
- [ ] 中心数值显示正确
- [ ] 最大值记录正常

#### 切换稳定性
- [ ] 切换过程无卡顿
- [ ] 历史数据正确清空
- [ ] 无频繁抖动切换

### 3. 手动档位对比测试

#### 测试目的
验证手动档位功能未受影响

#### 测试步骤
```bash
1. 选择"10A"手动档位
2. 观察超量程处理 (>10A的随机值)
3. 选择"500A"手动档位  
4. 观察量程内和超量程的表现
5. 选择"5000A"手动档位
6. 观察全量程覆盖情况
```

#### 验证点
- [ ] 手动档位不会自动切换
- [ ] 超量程值被正确限制在量程上限
- [ ] 仪表盘指针不会超出刻度范围

## 📊 预期测试结果

### 自动模式下的典型表现

#### 随机值示例与预期行为
```
随机值: 3.2A    → 档位: 10A   → 仪表盘: 0-10A,   指针: 3.2A位置
随机值: 125.6A  → 档位: 500A  → 仪表盘: 0-500A,  指针: 125.6A位置  
随机值: 1250.8A → 档位: 5000A → 仪表盘: 0-5000A, 指针: 1250.8A位置
随机值: 4999.9A → 档位: 5000A → 仪表盘: 0-5000A, 指针: 4999.9A位置
```

#### 切换阈值验证
```
7.9A → 10A档位   (低于8A阈值)
8.1A → 500A档位  (高于8A阈值，触发上切)
4.9A → 10A档位   (低于5A阈值，触发下切)
399A → 500A档位  (低于400A阈值)
401A → 5000A档位 (高于400A阈值，触发上切)
299A → 500A档位  (低于300A阈值，触发下切)
```

## 🐛 常见问题排查

### 问题1: 自动切换不工作
**可能原因**:
- 未选择"自动"档位
- 自动量程功能未启用

**排查方法**:
```cpp
// 在addSample()中添加调试日志
logDebug(QString("Current gear: %1, Auto enabled: %2, Current value: %3")
         .arg(m_eCurrentRangeGear).arg(m_bAutoRangeEnabled).arg(fCurrentValue));
```

### 问题2: 频繁切换
**可能原因**:
- 随机值在阈值附近波动
- 防抖机制失效

**排查方法**:
- 观察切换日志的频率
- 检查滞回阈值设置

### 问题3: 随机值范围异常
**可能原因**:
- 随机数生成算法错误
- 数据类型转换问题

**排查方法**:
```cpp
// 在readCurrentDetectionData()中添加日志
logDebug(QString("Generated random values: Ground=%1A, Load=%2A")
         .arg(fGroundCurrentRandom).arg(fLoadCurrentRandom));
```

## 📈 性能监控

### 关键指标
- **切换延迟**: 从检测到切换完成的时间
- **切换频率**: 单位时间内的切换次数  
- **内存使用**: 数据存储和处理的内存占用
- **CPU使用**: 随机数生成和切换判断的CPU开销

### 监控方法
```cpp
// 在switchToRange()中添加性能日志
QTime switchTime = QTime::currentTime();
// ... 切换逻辑 ...
int elapsedMs = switchTime.msecsTo(QTime::currentTime());
logDebug(QString("Range switch completed in %1ms").arg(elapsedMs));
```

## ✅ 测试检查清单

### 基础功能
- [ ] 随机数生成正常 (0-5000A范围)
- [ ] 自动档位选择正确
- [ ] 手动档位功能正常
- [ ] 配置保存和加载正常

### 自动切换
- [ ] 小电流自动选择10A档位
- [ ] 中等电流自动选择500A档位
- [ ] 大电流自动选择5000A档位
- [ ] 跨档位切换正常

### 界面表现  
- [ ] 仪表盘量程自动调整
- [ ] 指针位置准确
- [ ] 数值显示正确
- [ ] 切换过程平滑

### 稳定性
- [ ] 长时间运行稳定
- [ ] 无内存泄漏
- [ ] 无异常崩溃
- [ ] 防抖机制有效

通过这个全面的测试，可以验证随机电流数据生成和自动量程切换功能是否正常工作！
